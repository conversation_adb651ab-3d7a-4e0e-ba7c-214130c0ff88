{"welcome": {"title": "Hello Functionlander!", "appTitle": "Blox App", "disclaimer": "By using this product you agree to the terms and conditions at fx.land/terms, and would not hold Functionland liable for data loss.", "termsButton": "Terms & Conditions", "setupButton": "Agree & Setup my Blox"}, "connectToWallet": {"title": "Connect To Wallet", "description": "App needs notification permission to connect your wallet and perform data sync. Tap allow in the next prompt.", "allowButton": "Allow Notifications"}, "linkPassword": {"title": "Set Identity", "yourIdentity": "Your Identity", "password": "Password", "signature": "Signature", "warning": "Make sure to safeguard this password and the chain you used, it's the key to decrypt your data from new devices", "passwordRisk": "I understand the risk of losing my password", "metamaskOpen": "I already opened Metamask app before clicking Sign", "connectingWallet": "Connecting Wallet...", "walletConnectionInProgress": "Wallet connection in progress, click to move back to the app", "error": "Error", "unableToSignWallet": "Unable to sign the wallet address!", "signWithMetamask": "Sign with MetaMask", "cancel": "Cancel", "signManually": "Sign Manually", "submit": "Submit", "connectToBlox": "Connect to Blox", "reconnectExisting": "Reconnect to existing blox", "bluetoothCommands": "Bluetooth commands", "skipManualSetup": "Skip to manual setup"}, "connectToBlox": {"title": "Connect to Blox", "checking": "Checking connection...", "connected": "Connected", "failed": "Unable to connect to Hotspot", "notConnected": "Not Connected", "bleConnecting": "Searching for Blox device...", "bleConnected": "Connected to Blox via Bluetooth", "bleFailed": "Unable to connect via Bluetooth, trying WiFi...", "hotspotInstructions": "- Please turn your Blox on and connect your phone to the Blox's hotspot manually, and turn off mobile data.", "formatInstructions": "- Make sure you have internal or external storage attached and format is either 'ext4' or 'vFat'.", "waitForBlueLight": "After first boot please wait for 10 minutes until Blox flashes 'light-blue'", "connectedMessage": "Now you are connected to Blox. Please wait...", "back": "Back", "continue": "Continue"}, "connectToWifi": {"title": "Connect to Wi-Fi", "manualEntry": "Manually Enter Wifi Name", "showNetworks": "Show Network Names", "enterWifiName": "Enter Wifi Name", "enterPasswordFor": "Enter Password for", "searching": "Searching Wi-Fi Network", "selectNetwork": "Select Wi-Fi Network", "back": "Back", "next": "Next"}, "setBloxAuthorizer": {"title": "Set Blox Owner", "description": "Adding the Blox App Peer ID as an owner on the Blox", "networkError": "In some cases you need to turn the mobile data off, please make sure the phone is connected to the Blox's Hotspot and mobile data/VPN is off, Then press back and try again please", "updateNeeded": "An update is awaiting a manual restart to be applied. You should unplug and plug back your blox to restart it and then try again.", "backendUpdate": "You should update your blox backend, Please press 'Skip' button and connect it to your Wifi network.", "storageNeeded": "To proceed successfully you need to attach an external storage to the Blox!", "appPeerId": "The Blox App Peer ID", "generating": "Generating the app peerId...", "bloxPeerId": "Your Blox Peer ID", "enterBloxPeerId": "Enter Your Blox Peer ID", "setBloxName": "Set Blox name", "hardDisk": "Hard Disk", "bloxSetUp": "Blox Set Up", "formatDisk": "Format Disk", "skip": "<PERSON><PERSON>", "skipAuthorization": "Skip Authorization", "skipDescription": "The Skip is only intended when you are instructed to do so by the support team. Please enter the code given to you:", "cancel": "Cancel", "invalidCode": "Invalid Code", "invalidCodeMessage": "The code you entered is incorrect. Please contact support if you need assistance.", "confirm": "Confirm", "back": "Back", "setAuthorizer": "Set Authorizer", "next": "Next", "bloxUnitPrefix": "Blox Unit", "noBleDevicesConnected": "No BLE devices connected", "unableToGetProperties": "Unable to get the blox properties!", "bloxPeerIdInvalid": "Blox peerId is invalid!"}, "setupComplete": {"completing": "Completing setup", "connectPhone": "Connect your phone to internet (wifi) to proceed now...", "reachingBlox": "Reaching Blox #{{number}}...", "greenLed": "Is you blox LED 'green' but you see this message?", "internetReminder": "Make sure your phone is connected to the internet and then try again", "lightBlueLed": "Is you blox flashing 'light-blue'? You probably entered wrong wifi password", "notReachable": "Your Blox is not reachable, seems it is not connected to the internet! Please turn your blox off and then turn it on and make sure it is on Hotspot mode, then try to reconnect the blox to the Wi-Fi", "updating": "Your blox is updating. Please wait for an hour for the update to complete.", "disconnectHotspot": "Meanwhile, feel free to disconnect your phone from FxBlox hotspot.", "homeScreen": "Home Screen", "congratulations": "Congratulations", "setupComplete": "Setup Complete", "home": "Home", "checkInternet": "Check internet connectivity", "wrongPassword": "Entered Wrong Password? Go Back", "checkConnection": "Check Connection Again", "cyanFlashing": "If <PERSON><PERSON><PERSON> is flashing '<PERSON><PERSON>', it probably means you have entered the wrong password for your wifi. Connect to 'FxBlox' Wifi again and retry.", "back": "Back", "reconnectWifi": "Reconnect Blox to Wi-Fi", "notConnectedToHotspot": "It seems you are no longer connected to Hotspot, check if FxBlox hotspot is still available, if not, blox is already connected to internet and you can go to Home", "unableToInitialize": "Unable to initialize the fula network! error: {{error}} for fulaIsReady={{fulaIsReady}}"}, "connectToExistingBlox": {"title": "Bloxs in your network", "selectBloxs": "Select bloxs you want to add", "bloxUnitPrefix": "Blox unit", "ip": "IP", "peerId": "PEER ID", "hardwareId": "HARDWARE ID", "authorized": "Authorized", "notAuthorized": "Not Authorized", "checking": "Checking...", "alreadyExist": "Already exist", "addSelectedBloxs": "Add selected blox(s)", "generateAppPeerIdError": "ConnectToExistingBloxScreen:generateAppPeerId: "}, "checkConnection": {"verifyingConnectionWith": "Verifying connection with {{ssid}}", "successfullyConnected": "Successfully connected to {{ssid}}.", "verifyingConnection": "Verifying connection...", "couldntConnect": "Couldn't connect with {{ssid}}.", "couldntConnectTryAgain": "Couldn't connect with {{ssid}}. Please try again.", "connectingWith": "Connecting with {{ssid}}...", "allDone": "All done\nYour device is connected with success!"}}