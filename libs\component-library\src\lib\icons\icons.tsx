import React from 'react';
import { Circle, Path } from 'react-native-svg';
import { FxSvg, FxSvgProps } from '../svg/svg';

export const FxChevronDownIcon = (props: FxSvgProps) => (
  <FxSvg width={12} height={7} viewBox="0 0 12 7" fill="none" {...props}>
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M6 5.5L6.35355 5.85355L6 6.20711L5.64645 5.85355L6 5.5ZM11.3536 0.853553L6.35355 5.85355L5.64645 5.14645L10.6464 0.146447L11.3536 0.853553ZM5.64645 5.85355L0.646446 0.853554L1.35355 0.146447L6.35355 5.14645L5.64645 5.85355Z"
    />
  </FxSvg>
);
export const FxChevronUpIcon = (props: FxSvgProps) => (
  <FxSvg width={16} height={16} viewBox="0 0 16 16" fill="none" {...props}>
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M2.31344 10.8819C2.11818 10.6866 2.11818 10.37 2.31344 10.1748L7.17537 5.31284C7.63098 4.85723 8.36967 4.85723 8.82528 5.31284L13.6872 10.1748C13.8825 10.37 13.8825 10.6866 13.6872 10.8819C13.492 11.0771 13.1754 11.0771 12.9801 10.8819L8.11818 6.01995C8.05309 5.95486 7.94756 5.95486 7.88248 6.01994L3.02055 10.8819C2.82528 11.0771 2.5087 11.0771 2.31344 10.8819Z"
    />
  </FxSvg>
);

export const FxChevronRightIcon = (props: FxSvgProps) => (
  <FxSvg width={16} height={16} viewBox="0 0 16 16" {...props}>
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M5.11788 2.3131C5.31314 2.11784 5.62973 2.11784 5.82499 2.3131L10.6869 7.17503C11.1425 7.63064 11.1425 8.36934 10.6869 8.82495L5.82499 13.6869C5.62973 13.8821 5.31314 13.8821 5.11788 13.6869C4.92262 13.4916 4.92262 13.175 5.11788 12.9798L9.97981 8.11784C10.0449 8.05275 10.0449 7.94723 9.97981 7.88214L5.11788 3.02021C4.92262 2.82495 4.92262 2.50837 5.11788 2.3131Z"
    />
  </FxSvg>
);

export const FxChevronLeftIcon = (props: FxSvgProps) => (
  <FxSvg width={16} height={16} viewBox="0 0 16 16" {...props}>
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M10.8821 2.31312C10.6869 2.11786 10.3703 2.11786 10.175 2.31312L5.31308 7.17505C4.85747 7.63066 4.85747 8.36935 5.31308 8.82496L10.175 13.6869C10.3703 13.8822 10.6869 13.8822 10.8821 13.6869C11.0774 13.4916 11.0774 13.175 10.8821 12.9798L6.02019 8.11786C5.9551 8.05277 5.9551 7.94724 6.02019 7.88215L10.8821 3.02023C11.0774 2.82496 11.0774 2.50838 10.8821 2.31312Z"
    />
  </FxSvg>
);

export const FxCloseIcon = (props: FxSvgProps) => (
  <FxSvg width="18" height="18" viewBox="0 0 18 18" {...props}>
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M16.4697 17.5303C16.7626 17.8232 17.2374 17.8232 17.5303 17.5303C17.8232 17.2374 17.8232 16.7626 17.5303 16.4697L10.0607 9L17.5303 1.53033C17.8232 1.23744 17.8232 0.762564 17.5303 0.469669C17.2374 0.176777 16.7626 0.176777 16.4697 0.469669L9 7.93934L1.53033 0.469669C1.23744 0.176777 0.762564 0.176777 0.469669 0.469669C0.176777 0.762564 0.176777 1.23744 0.469669 1.53033L7.93934 9L0.46967 16.4697C0.176777 16.7626 0.176777 17.2374 0.46967 17.5303C0.762563 17.8232 1.23744 17.8232 1.53033 17.5303L9 10.0607L16.4697 17.5303Z"
    />
  </FxSvg>
);

export const FxDownArrowIcon = (props: FxSvgProps) => (
  <FxSvg width={14} height={9} viewBox="0 0 14 8" {...props}>
    <Path d="M7 7.49585L14 0.495849H0L7 7.49585Z" />
  </FxSvg>
);

export const FxExclamationIcon = (props: FxSvgProps) => (
  <FxSvg width={12} height={12} viewBox="0 0 12 12" {...props}>
    <Path d="M6 11.25C4.60761 11.25 3.27226 10.6969 2.28769 9.71231C1.30312 8.72774 0.75 7.39239 0.75 6C0.75 4.60761 1.30312 3.27226 2.28769 2.28769C3.27226 1.30312 4.60761 0.75 6 0.75C7.39239 0.75 8.72774 1.30312 9.71231 2.28769C10.6969 3.27226 11.25 4.60761 11.25 6C11.25 7.39239 10.6969 8.72774 9.71231 9.71231C8.72774 10.6969 7.39239 11.25 6 11.25ZM6 12C7.5913 12 9.11742 11.3679 10.2426 10.2426C11.3679 9.11742 12 7.5913 12 6C12 4.4087 11.3679 2.88258 10.2426 1.75736C9.11742 0.632141 7.5913 0 6 0C4.4087 0 2.88258 0.632141 1.75736 1.75736C0.632141 2.88258 0 4.4087 0 6C0 7.5913 0.632141 9.11742 1.75736 10.2426C2.88258 11.3679 4.4087 12 6 12Z" />
    <Path d="M5.25146 8.25C5.25146 8.1515 5.27086 8.05398 5.30856 7.96298C5.34625 7.87199 5.40149 7.78931 5.47113 7.71967C5.54078 7.65002 5.62346 7.59478 5.71445 7.55709C5.80545 7.5194 5.90297 7.5 6.00146 7.5C6.09996 7.5 6.19748 7.5194 6.28848 7.55709C6.37947 7.59478 6.46215 7.65002 6.53179 7.71967C6.60144 7.78931 6.65668 7.87199 6.69437 7.96298C6.73207 8.05398 6.75146 8.1515 6.75146 8.25C6.75146 8.44891 6.67245 8.63967 6.53179 8.78033C6.39114 8.92098 6.20038 9 6.00146 9C5.80255 9 5.61179 8.92098 5.47113 8.78033C5.33048 8.63967 5.25146 8.44891 5.25146 8.25ZM5.32496 3.74625C5.31498 3.65163 5.32499 3.55597 5.35437 3.46547C5.38374 3.37497 5.43181 3.29166 5.49545 3.22094C5.5591 3.15022 5.63691 3.09367 5.72382 3.05496C5.81074 3.01625 5.90482 2.99625 5.99996 2.99625C6.09511 2.99625 6.18919 3.01625 6.27611 3.05496C6.36302 3.09367 6.44083 3.15022 6.50448 3.22094C6.56812 3.29166 6.61619 3.37497 6.64556 3.46547C6.67494 3.55597 6.68495 3.65163 6.67496 3.74625L6.41247 6.3765C6.40365 6.47983 6.35637 6.57608 6.27998 6.64622C6.2036 6.71637 6.10367 6.75529 5.99996 6.75529C5.89626 6.75529 5.79633 6.71637 5.71995 6.64622C5.64356 6.57608 5.59628 6.47983 5.58746 6.3765L5.32496 3.74625Z" />
  </FxSvg>
);

export const FxFilterIcon = (props: FxSvgProps) => (
  <FxSvg width={16} height={16} viewBox="0 0 16 16" {...props}>
    <Path d="M9.25 15.5H6.75C6.41848 15.5 6.10054 15.3683 5.86612 15.1339C5.6317 14.8995 5.5 14.5815 5.5 14.25V9.50625L0.86875 4.875C0.634008 4.64166 0.501395 4.32474 0.5 3.99375V1.75C0.5 1.41848 0.631696 1.10054 0.866116 0.866116C1.10054 0.631696 1.41848 0.5 1.75 0.5H14.25C14.5815 0.5 14.8995 0.631696 15.1339 0.866116C15.3683 1.10054 15.5 1.41848 15.5 1.75V3.99375C15.4986 4.32474 15.366 4.64166 15.1313 4.875L10.5 9.50625V14.25C10.5 14.5815 10.3683 14.8995 10.1339 15.1339C9.89946 15.3683 9.58152 15.5 9.25 15.5ZM1.75 1.75V3.99375L6.75 8.99375V14.25H9.25V8.99375L14.25 3.99375V1.75H1.75Z" />
  </FxSvg>
);

export const FxLoadingSpinnerIcon = (props: FxSvgProps) => (
  <FxSvg width={16} height={16} viewBox="0 0 16 16" {...props}>
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M7.83045 1.8351C7.86006 2.10965 7.6615 2.35622 7.38695 2.38583C4.5568 2.69107 2.35303 5.08851 2.35303 7.99999C2.35303 10.9115 4.5568 13.3089 7.38695 13.6141C7.6615 13.6438 7.86006 13.8903 7.83045 14.1649C7.80084 14.4394 7.55427 14.638 7.27972 14.6084C3.94724 14.249 1.35303 11.4278 1.35303 7.99999C1.35303 4.57221 3.94724 1.75102 7.27972 1.3916C7.55427 1.36199 7.80084 1.56055 7.83045 1.8351Z"
    />
  </FxSvg>
);

export const FxInvertedCheckIcon = (props: FxSvgProps) => (
  <FxSvg width={20} height={20} viewBox="0 0 20 20" {...props}>
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M10 0C4.47727 0 0 4.47727 0 10C0 15.5227 4.47727 20 10 20C15.5227 20 20 15.5227 20 10C20 4.47727 15.5227 0 10 0ZM14.3345 8.30909C14.4144 8.21786 14.4751 8.11158 14.5133 7.99652C14.5514 7.88145 14.5661 7.75992 14.5566 7.63908C14.5471 7.51823 14.5135 7.40051 14.4578 7.29284C14.4021 7.18518 14.3254 7.08973 14.2323 7.01213C14.1392 6.93453 14.0315 6.87633 13.9155 6.84097C13.7996 6.8056 13.6777 6.79378 13.5572 6.80621C13.4366 6.81863 13.3197 6.85504 13.2134 6.9133C13.1071 6.97156 13.0135 7.0505 12.9382 7.14545L9.02909 11.8355L7.00636 9.81182C6.83491 9.64622 6.60527 9.55459 6.36691 9.55666C6.12855 9.55873 5.90054 9.65434 5.73198 9.82289C5.56343 9.99145 5.46782 10.2195 5.46575 10.4578C5.46368 10.6962 5.55531 10.9258 5.72091 11.0973L8.44818 13.8245C8.53751 13.9138 8.64445 13.9835 8.7622 14.0291C8.87994 14.0748 9.00591 14.0954 9.13206 14.0897C9.25822 14.084 9.3818 14.052 9.49492 13.9959C9.60804 13.9397 9.70823 13.8606 9.78909 13.7636L14.3345 8.30909Z"
    />
  </FxSvg>
);

export const FxCheckIcon = (props: FxSvgProps) => (
  <FxSvg width={24} height={24} viewBox="0 0 24 24" {...props}>
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M15.513 9.45285C15.8151 9.73615 15.8305 10.2108 15.5472 10.513L11.7972 14.513C11.5192 14.8095 11.0555 14.8306 10.7517 14.5606L8.50174 12.5606C8.19215 12.2854 8.16426 11.8113 8.43945 11.5017C8.71464 11.1921 9.18869 11.1643 9.49828 11.4394L11.2024 12.9542L14.4529 9.48705C14.7362 9.18486 15.2108 9.16955 15.513 9.45285Z"
    />
  </FxSvg>
);

export const FxEditIcon = (props: FxSvgProps) => (
  <FxSvg width={20} height={20} viewBox="0 0 20 20" {...props}>
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M10 20C15.5228 20 20 15.5228 20 10C20 4.47715 15.5228 0 10 0C4.47715 0 0 4.47715 0 10C0 15.5228 4.47715 20 10 20ZM13.6594 9.17088L7.70084 15.1295C7.61877 15.2115 7.51553 15.2699 7.40269 15.2981L4.04156 15.9585L4.70199 12.5968C4.73032 12.4842 4.78859 12.3814 4.87062 12.2992L10.8292 6.34066L13.6594 9.17088ZM15.9585 6.34066C15.9585 6.6807 15.8234 7.00682 15.583 7.2473L14.5661 8.26423L11.7359 5.43402L12.7528 4.41709C12.9933 4.17668 13.3194 4.04163 13.6594 4.04163C13.9995 4.04163 14.3256 4.17668 14.5661 4.41709L15.583 5.43402C15.8234 5.6745 15.9585 6.00062 15.9585 6.34066Z"
    />
  </FxSvg>
);
export const FxSuccessIcon = (props: FxSvgProps) => (
  <FxSvg width={24} height={24} viewBox="0 0 24 24" {...props}>
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M12 2C6.47727 2 2 6.47727 2 12C2 17.5227 6.47727 22 12 22C17.5227 22 22 17.5227 22 12C22 6.47727 17.5227 2 12 2ZM16.3345 10.3091C16.4144 10.2179 16.4751 10.1116 16.5133 9.99652C16.5514 9.88145 16.5661 9.75992 16.5566 9.63908C16.5471 9.51823 16.5135 9.40051 16.4578 9.29284C16.4021 9.18517 16.3254 9.08973 16.2323 9.01213C16.1392 8.93453 16.0315 8.87633 15.9155 8.84097C15.7996 8.8056 15.6777 8.79378 15.5572 8.80621C15.4366 8.81863 15.3197 8.85504 15.2134 8.9133C15.1071 8.97156 15.0135 9.0505 14.9382 9.14545L11.0291 13.8355L9.00636 11.8118C8.83491 11.6462 8.60527 11.5546 8.36691 11.5567C8.12855 11.5587 7.90054 11.6543 7.73198 11.8229C7.56343 11.9914 7.46782 12.2195 7.46575 12.4578C7.46368 12.6962 7.55531 12.9258 7.72091 13.0973L10.4482 15.8245C10.5375 15.9138 10.6445 15.9835 10.7622 16.0291C10.8799 16.0748 11.0059 16.0954 11.1321 16.0897C11.2582 16.084 11.3818 16.052 11.4949 15.9959C11.608 15.9397 11.7082 15.8606 11.7891 15.7636L16.3345 10.3091Z"
    />
  </FxSvg>
);

export const FxInfoIcon = (props: FxSvgProps) => (
  <FxSvg width={24} height={24} viewBox="0 0 24 24" {...props}>
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M12 2C6.47727 2 2 6.47727 2 12C2 17.5227 6.47727 22 12 22C17.5227 22 22 17.5227 22 12C22 6.47727 17.5227 2 12 2ZM11.5455 6.54545C11.3043 6.54545 11.0731 6.64123 10.9026 6.81172C10.7321 6.98221 10.6364 7.21344 10.6364 7.45455C10.6364 7.69565 10.7321 7.92688 10.9026 8.09737C11.0731 8.26786 11.3043 8.36364 11.5455 8.36364H12C12.2411 8.36364 12.4723 8.26786 12.6428 8.09737C12.8133 7.92688 12.9091 7.69565 12.9091 7.45455C12.9091 7.21344 12.8133 6.98221 12.6428 6.81172C12.4723 6.64123 12.2411 6.54545 12 6.54545H11.5455ZM10.1818 10.1818C9.94071 10.1818 9.70948 10.2776 9.53899 10.4481C9.36851 10.6186 9.27273 10.8498 9.27273 11.0909C9.27273 11.332 9.36851 11.5632 9.53899 11.7337C9.70948 11.9042 9.94071 12 10.1818 12H11.0909V14.7273H10.1818C9.94071 14.7273 9.70948 14.8231 9.53899 14.9935C9.36851 15.164 9.27273 15.3953 9.27273 15.6364C9.27273 15.8775 9.36851 16.1087 9.53899 16.2792C9.70948 16.4497 9.94071 16.5455 10.1818 16.5455H13.8182C14.0593 16.5455 14.2905 16.4497 14.461 16.2792C14.6315 16.1087 14.7273 15.8775 14.7273 15.6364C14.7273 15.3953 14.6315 15.164 14.461 14.9935C14.2905 14.8231 14.0593 14.7273 13.8182 14.7273H12.9091V11.0909C12.9091 10.8498 12.8133 10.6186 12.6428 10.4481C12.4723 10.2776 12.2411 10.1818 12 10.1818H10.1818Z"
    />
  </FxSvg>
);

export const FxWarningIcon = (props: FxSvgProps) => (
  <FxSvg width={24} height={24} viewBox="0 0 24 24" {...props}>
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M12 2C6.47727 2 2 6.47727 2 12C2 17.5227 6.47727 22 12 22C17.5227 22 22 17.5227 22 12C22 6.47727 17.5227 2 12 2ZM12.9091 7.45455C12.9091 7.21344 12.8133 6.98221 12.6428 6.81172C12.4723 6.64123 12.2411 6.54545 12 6.54545C11.7589 6.54545 11.5277 6.64123 11.3572 6.81172C11.1867 6.98221 11.0909 7.21344 11.0909 7.45455V12.9091C11.0909 13.1502 11.1867 13.3814 11.3572 13.5519C11.5277 13.7224 11.7589 13.8182 12 13.8182C12.2411 13.8182 12.4723 13.7224 12.6428 13.5519C12.8133 13.3814 12.9091 13.1502 12.9091 12.9091V7.45455ZM12.9091 16.0909C12.9091 15.8498 12.8133 15.6186 12.6428 15.4481C12.4723 15.2776 12.2411 15.1818 12 15.1818C11.7589 15.1818 11.5277 15.2776 11.3572 15.4481C11.1867 15.6186 11.0909 15.8498 11.0909 16.0909V16.5455C11.0909 16.7866 11.1867 17.0178 11.3572 17.1883C11.5277 17.3588 11.7589 17.4545 12 17.4545C12.2411 17.4545 12.4723 17.3588 12.6428 17.1883C12.8133 17.0178 12.9091 16.7866 12.9091 16.5455V16.0909Z"
    />
  </FxSvg>
);
export const FxCircleFilledIcon = (props: FxSvgProps) => (
  <FxSvg width={24} height={24} viewBox="0 0 24 24" fill="none" {...props}>
    <Circle cx="12" cy="12" r="10" />
  </FxSvg>
);

export const FxErrorIcon = (props: FxSvgProps) => (
  <FxSvg width={24} height={24} viewBox="0 0 24 24" {...props}>
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M12 2C6.47727 2 2 6.47727 2 12C2 17.5227 6.47727 22 12 22C17.5227 22 22 17.5227 22 12C22 6.47727 17.5227 2 12 2ZM15.37 9.91545C15.5356 9.744 15.6272 9.51436 15.6252 9.276C15.6231 9.03764 15.5275 8.80963 15.3589 8.64107C15.1904 8.47252 14.9624 8.37691 14.724 8.37484C14.4856 8.37277 14.256 8.4644 14.0845 8.63L12 10.7145L9.91545 8.63C9.83159 8.54317 9.73128 8.47392 9.62037 8.42627C9.50946 8.37863 9.39016 8.35355 9.26946 8.3525C9.14875 8.35145 9.02904 8.37445 8.91731 8.42016C8.80559 8.46587 8.70409 8.53338 8.61873 8.61873C8.53338 8.70409 8.46587 8.80559 8.42016 8.91731C8.37445 9.02904 8.35145 9.14875 8.3525 9.26946C8.35355 9.39016 8.37863 9.50946 8.42627 9.62037C8.47392 9.73128 8.54317 9.83159 8.63 9.91545L10.7145 12L8.63 14.0845C8.54317 14.1684 8.47392 14.2687 8.42627 14.3796C8.37863 14.4905 8.35355 14.6098 8.3525 14.7305C8.35145 14.8513 8.37445 14.971 8.42016 15.0827C8.46587 15.1944 8.53338 15.2959 8.61873 15.3813C8.70409 15.4666 8.80559 15.5341 8.91731 15.5798C9.02904 15.6255 9.14875 15.6486 9.26946 15.6475C9.39016 15.6465 9.50946 15.6214 9.62037 15.5737C9.73128 15.5261 9.83159 15.4568 9.91545 15.37L12 13.2855L14.0845 15.37C14.256 15.5356 14.4856 15.6272 14.724 15.6252C14.9624 15.6231 15.1904 15.5275 15.3589 15.3589C15.5275 15.1904 15.6231 14.9624 15.6252 14.724C15.6272 14.4856 15.5356 14.256 15.37 14.0845L13.2855 12L15.37 9.91545Z"
    />
  </FxSvg>
);
export const FxFolderIcon = (props: FxSvgProps) => (
  <FxSvg width={24} height={24} viewBox="0 0 24 24" {...props}>
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M8.99905 2.90625C9.48287 2.90609 9.95716 3.04071 10.3688 3.29502C10.7804 3.54937 11.113 3.91337 11.3294 4.34619C11.3294 4.34625 11.3293 4.34614 11.3294 4.34619L12.1582 6.00382C12.2498 6.18728 12.3909 6.34175 12.5653 6.4496C12.7398 6.55744 12.9408 6.61457 13.1459 6.61458C13.1459 6.61458 13.1459 6.61458 13.1459 6.61458H19.4167C20.1074 6.61458 20.7697 6.88895 21.2581 7.37733C21.7465 7.8657 22.0209 8.52808 22.0209 9.21875V18.4896C22.0209 19.1803 21.7465 19.8426 21.2581 20.331C20.7697 20.8194 20.1074 21.0938 19.4167 21.0938H4.58335C3.89269 21.0938 3.23031 20.8194 2.74193 20.331C2.25355 19.8426 1.97919 19.1803 1.97919 18.4896V5.51042C1.97919 4.81975 2.25355 4.15737 2.74193 3.66899C3.23031 3.18062 3.89269 2.90625 4.58335 2.90625H8.99905C8.99903 2.90625 8.99907 2.90625 8.99905 2.90625ZM9.5803 4.57109C9.40575 4.46324 9.20459 4.40615 8.9994 4.40625H4.58335C4.29051 4.40625 4.00966 4.52258 3.80259 4.72965C3.59552 4.93672 3.47919 5.21757 3.47919 5.51042V18.4896C3.47919 18.7824 3.59552 19.0633 3.80259 19.2703C4.00966 19.4774 4.29051 19.5938 4.58335 19.5938H19.4167C19.7095 19.5938 19.9904 19.4774 20.1975 19.2703C20.4045 19.0633 20.5209 18.7824 20.5209 18.4896V9.21875C20.5209 8.92591 20.4045 8.64506 20.1975 8.43799C19.9904 8.23091 19.7095 8.11458 19.4167 8.11458H13.1459C12.6622 8.11457 12.188 7.97983 11.7766 7.72547C11.3652 7.47114 11.0327 7.10727 10.8165 6.67464C10.8165 6.67459 10.8165 6.67469 10.8165 6.67464L9.98771 5.01701C9.89599 4.83347 9.75486 4.67895 9.5803 4.57109Z"
    />
  </FxSvg>
);
export const FxDocumentIcon = (props: FxSvgProps) => (
  <FxSvg width={24} height={24} viewBox="0 0 24 24" fill="none" {...props}>
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M13.9289 2.21429L18.9289 7.21429C19.0717 7.35714 19.1431 7.5 19.1431 7.71429V20.5714C19.1431 21.3571 18.5003 22 17.7146 22H6.28599C5.50028 22 4.85742 21.3571 4.85742 20.5714V3.42857C4.85742 2.64286 5.50028 2 6.28599 2H13.4289C13.6431 2 13.786 2.07143 13.9289 2.21429ZM17.4289 7.71429L13.4289 3.71429V7.71429H17.4289ZM6.28599 20.5714H17.7146V9.14286H13.4289C12.6431 9.14286 12.0003 8.5 12.0003 7.71429V3.42857H6.28599V20.5714ZM16.286 16.2857H7.71456V17.7143H16.286V16.2857ZM16.286 12H7.71456V13.4286H16.286V12Z"
    />
  </FxSvg>
);
export const FxPDFIcon = (props: FxSvgProps) => (
  <FxSvg width={24} height={24} viewBox="0 0 24 24" {...props}>
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M16.2332 7.44097C16.2702 7.52728 16.2881 7.62059 16.2855 7.71446V10.5716H14.857V9.14301H10.5713C10.1928 9.14188 9.83009 8.99101 9.56243 8.72335C9.29477 8.45569 9.1439 8.09299 9.14277 7.71446V3.4288H3.42855V20.5714H14.857V22H3.42855C3.04968 22 2.68632 21.8495 2.41841 21.5816C2.15051 21.3137 2 20.9503 2 20.5714V3.4288C2.00113 3.05027 2.152 2.68757 2.41966 2.41991C2.68732 2.15224 3.05003 2.00137 3.42855 2.00024H10.5713C10.6652 1.99767 10.7585 2.0155 10.8448 2.0525C10.9312 2.08949 11.0084 2.14477 11.0713 2.21453L16.0713 7.21447C16.141 7.27738 16.1962 7.35465 16.2332 7.44097ZM10.5713 3.71451V7.71446H14.5713L10.5713 3.71451ZM21.9998 12.0001V13.4287H19.1426V14.8572H21.2855V16.2858H19.1426V19.1429H17.7141V12.0001H21.9998ZM11.2856 19.1429H14.1427C14.7108 19.1423 15.2556 18.9164 15.6573 18.5146C16.059 18.1129 16.285 17.5682 16.2855 17V14.1429C16.285 13.5748 16.059 13.0301 15.6573 12.6284C15.2556 12.2266 14.7108 12.0007 14.1427 12.0001H11.2856V19.1429ZM14.1427 17.7143H12.7142V13.4287H14.1427C14.3321 13.4289 14.5137 13.5042 14.6476 13.6381C14.7815 13.772 14.8568 13.9536 14.857 14.1429V17C14.8568 17.1894 14.7815 17.371 14.6476 17.5049C14.5137 17.6388 14.3321 17.7141 14.1427 17.7143ZM4.85711 12.0001H8.42849C8.80725 12.0005 9.17039 12.1511 9.43822 12.4189C9.70604 12.6868 9.85667 13.0499 9.85705 13.4287V15.5715C9.85648 15.9502 9.70579 16.3132 9.43801 16.581C9.17022 16.8488 8.8072 16.9995 8.42849 17H6.28566V19.1429H4.85711V12.0001ZM6.28566 13.4287V15.5715H8.42921L8.42849 13.4287H6.28566Z"
    />
  </FxSvg>
);
export const FxAudioIcon = (props: FxSvgProps) => (
  <FxSvg width={24} height={24} viewBox="0 0 24 24" {...props}>
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M5.1039 19.931H10.6211V21.3103H5.1039C4.73843 21.3092 4.38823 21.1636 4.1298 20.9051C3.87137 20.6467 3.7257 20.2965 3.72461 19.931V3.37953C3.7257 3.01405 3.87137 2.66386 4.1298 2.40543C4.38823 2.14699 4.73843 2.00133 5.1039 2.00023H12.0004C12.091 1.99775 12.1811 2.01497 12.2645 2.05069C12.3478 2.08641 12.4224 2.13978 12.4831 2.20713L17.3106 7.03465C17.378 7.0954 17.4313 7.17001 17.467 7.25335C17.5027 7.33668 17.52 7.42677 17.5175 7.51741V10.9656H16.1382V8.8967H12.0004C11.6349 8.89561 11.2847 8.74994 11.0263 8.49151C10.7678 8.23308 10.6222 7.88288 10.6211 7.51741V3.37953H5.1039V19.931ZM15.8624 7.51741L12.0004 3.65539V7.51741H15.8624ZM19.1554 21.8483C19.2777 21.9464 19.4297 21.9999 19.5865 22C19.7694 22 19.9448 21.9273 20.0741 21.798C20.2035 21.6687 20.2761 21.4933 20.2761 21.3104V12.345C20.2759 12.2152 20.2391 12.0881 20.17 11.9783C20.1008 11.8685 20.002 11.7805 19.8851 11.7243C19.7681 11.6681 19.6376 11.646 19.5087 11.6607C19.3797 11.6753 19.2575 11.726 19.1561 11.807L15.8969 14.4139H13.3797C13.1968 14.4139 13.0213 14.4865 12.892 14.6159C12.7627 14.7452 12.69 14.9206 12.69 15.1035V18.5518C12.69 18.7347 12.7627 18.9101 12.892 19.0394C13.0213 19.1688 13.1968 19.2414 13.3797 19.2414H15.8969L19.1554 21.8483ZM16.1382 17.8621H14.0693V15.7932H16.1382C16.2948 15.7929 16.4465 15.7394 16.5686 15.6415L18.8968 13.7794V19.8759L16.5693 18.0138C16.447 17.9158 16.295 17.8622 16.1382 17.8621Z"
    />
  </FxSvg>
);

export const FxImageIcon = (props: FxSvgProps) => (
  <FxSvg width={24} height={24} viewBox="0 0 24 24" {...props}>
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M18.9286 7.21429L13.9286 2.21429C13.7858 2.07143 13.6429 2 13.4286 2H6.28575C5.50003 2 4.85718 2.64286 4.85718 3.42857V20.5714C4.85718 21.3571 5.50003 22 6.28575 22H17.7143C18.5 22 19.1429 21.3571 19.1429 20.5714V7.71429C19.1429 7.5 19.0715 7.35714 18.9286 7.21429ZM13.4286 3.71429L17.4286 7.71429H13.4286V3.71429ZM9.47015 20.5714H6.28575V3.42857H12V7.71429C12 8.5 12.6429 9.14286 13.4286 9.14286H17.7143V15.9596C16.6509 15.1121 15.102 15.1648 14.0982 16.1285L9.47015 20.5714ZM11.6361 20.5714H17.7143V18.0562L16.8866 17.2284C16.4054 16.7473 15.6279 16.7394 15.137 17.2106L11.6361 20.5714ZM10.25 13.9375C10.9404 13.9375 11.5 13.3779 11.5 12.6875C11.5 11.9971 10.9404 11.4375 10.25 11.4375C9.55964 11.4375 9 11.9971 9 12.6875C9 13.3779 9.55964 13.9375 10.25 13.9375ZM10.25 15.4375C11.7688 15.4375 13 14.2063 13 12.6875C13 11.1687 11.7688 9.9375 10.25 9.9375C8.73122 9.9375 7.5 11.1687 7.5 12.6875C7.5 14.2063 8.73122 15.4375 10.25 15.4375Z"
    />
  </FxSvg>
);
export const FxDownloadingIcon = (props: FxSvgProps) => (
  <FxSvg width={16} height={16} viewBox="0 0 16 16" {...props}>
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M7.83045 1.8351C7.86006 2.10965 7.6615 2.35622 7.38695 2.38583C4.5568 2.69107 2.35303 5.08851 2.35303 7.99999C2.35303 10.9115 4.5568 13.3089 7.38695 13.6141C7.6615 13.6438 7.86006 13.8903 7.83045 14.1649C7.80084 14.4394 7.55427 14.638 7.27972 14.6084C3.94724 14.249 1.35303 11.4278 1.35303 7.99999C1.35303 4.57221 3.94724 1.75102 7.27972 1.3916C7.55427 1.36199 7.80084 1.56055 7.83045 1.8351ZM8.84513 1.8901C8.7855 2.15973 8.95573 2.42664 9.22536 2.48627C9.58445 2.56569 9.93066 2.67931 10.2605 2.82355C10.5135 2.9342 10.8083 2.8188 10.9189 2.5658C11.0296 2.3128 10.9142 2.018 10.6612 1.90734C10.2725 1.73737 9.86449 1.60346 9.44131 1.50987C9.17168 1.45024 8.90476 1.62047 8.84513 1.8901ZM11.5854 2.98161C11.4071 3.19245 11.4335 3.50794 11.6443 3.68626C12.1991 4.15545 12.662 4.72955 13.0025 5.37758C13.1309 5.62203 13.4332 5.71608 13.6777 5.58764C13.9221 5.4592 14.0162 5.15691 13.8877 4.91246C13.4869 4.14948 12.9423 3.47431 12.2901 2.92271C12.0792 2.74439 11.7637 2.77076 11.5854 2.98161ZM13.9382 6.32871C13.6672 6.38144 13.4902 6.64392 13.5429 6.91499C13.6112 7.26576 13.647 7.62849 13.647 8.00007C13.647 8.37166 13.6112 8.73439 13.5429 9.08516C13.4902 9.35623 13.6672 9.61871 13.9382 9.67144C14.2093 9.72416 14.4718 9.54717 14.5245 9.27611C14.6049 8.86276 14.647 8.43609 14.647 8.00007C14.647 7.56406 14.6049 7.13739 14.5245 6.72404C14.4718 6.45298 14.2093 6.27598 13.9382 6.32871ZM13.6777 10.4125C13.4332 10.2841 13.1309 10.3781 13.0025 10.6226C12.662 11.2706 12.1991 11.8447 11.6443 12.3139C11.4335 12.4922 11.4071 12.8077 11.5854 13.0185C11.7637 13.2294 12.0792 13.2558 12.2901 13.0774C12.9423 12.5258 13.4869 11.8507 13.8877 11.0877C14.0162 10.8432 13.9221 10.5409 13.6777 10.4125ZM10.9189 13.4343C10.8083 13.1813 10.5135 13.0659 10.2605 13.1766C9.93066 13.3208 9.58445 13.4345 9.22536 13.5139C8.95573 13.5735 8.7855 13.8404 8.84513 14.11C8.90476 14.3797 9.17168 14.5499 9.44131 14.4903C9.86449 14.3967 10.2725 14.2628 10.6612 14.0928C10.9142 13.9822 11.0296 13.6874 10.9189 13.4343ZM5.78186 8.50786C5.5866 8.70312 5.5866 9.0197 5.78186 9.21496L7.41074 10.8438C7.73618 11.1693 8.26382 11.1693 8.58926 10.8438L10.2181 9.21496C10.4134 9.0197 10.4134 8.70312 10.2181 8.50786C10.0229 8.31259 9.70629 8.31259 9.51103 8.50786L8.5 9.51889L8.5 5.41219C8.5 5.13605 8.27614 4.91219 8 4.91219C7.72386 4.91219 7.5 5.13605 7.5 5.41219L7.5 9.51889L6.48897 8.50786C6.29371 8.31259 5.97713 8.31259 5.78186 8.50786Z"
    />
  </FxSvg>
);
export const FxOptionsHorizontalIcon = (props: FxSvgProps) => (
  <FxSvg width={24} height={24} viewBox="0 0 24 24" fill="none" {...props}>
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M12 14C12.5304 14 13.0391 13.7893 13.4142 13.4142C13.7893 13.0391 14 12.5304 14 12C14 11.4696 13.7893 10.9609 13.4142 10.5858C13.0391 10.2107 12.5304 10 12 10C11.4696 10 10.9609 10.2107 10.5858 10.5858C10.2107 10.9609 10 11.4696 10 12C10 12.5304 10.2107 13.0391 10.5858 13.4142C10.9609 13.7893 11.4696 14 12 14ZM6 14C6.53043 14 7.03914 13.7893 7.41421 13.4142C7.78929 13.0391 8 12.5304 8 12C8 11.4696 7.78929 10.9609 7.41421 10.5858C7.03914 10.2107 6.53043 10 6 10C5.46957 10 4.96086 10.2107 4.58579 10.5858C4.21071 10.9609 4 11.4696 4 12C4 12.5304 4.21071 13.0391 4.58579 13.4142C4.96086 13.7893 5.46957 14 6 14ZM18 14C18.5304 14 19.0391 13.7893 19.4142 13.4142C19.7893 13.0391 20 12.5304 20 12C20 11.4696 19.7893 10.9609 19.4142 10.5858C19.0391 10.2107 18.5304 10 18 10C17.4696 10 16.9609 10.2107 16.5858 10.5858C16.2107 10.9609 16 11.4696 16 12C16 12.5304 16.2107 13.0391 16.5858 13.4142C16.9609 13.7893 17.4696 14 18 14Z"
    />
  </FxSvg>
);

export const FxOptionsVerticalIcon = (props: FxSvgProps) => (
  <FxSvg width={24} height={24} viewBox="0 0 24 24" fill="none" {...props}>
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M10 12C10 12.5304 10.2107 13.0391 10.5858 13.4142C10.9609 13.7893 11.4696 14 12 14C12.5304 14 13.0391 13.7893 13.4142 13.4142C13.7893 13.0391 14 12.5304 14 12C14 11.4696 13.7893 10.9609 13.4142 10.5858C13.0391 10.2107 12.5304 10 12 10C11.4696 10 10.9609 10.2107 10.5858 10.5858C10.2107 10.9609 10 11.4696 10 12ZM10 6C10 6.53043 10.2107 7.03914 10.5858 7.41421C10.9609 7.78929 11.4696 8 12 8C12.5304 8 13.0391 7.78929 13.4142 7.41421C13.7893 7.03914 14 6.53043 14 6C14 5.46957 13.7893 4.96086 13.4142 4.58579C13.0391 4.21071 12.5304 4 12 4C11.4696 4 10.9609 4.21071 10.5858 4.58579C10.2107 4.96086 10 5.46957 10 6ZM10 18C10 18.5304 10.2107 19.0391 10.5858 19.4142C10.9609 19.7893 11.4696 20 12 20C12.5304 20 13.0391 19.7893 13.4142 19.4142C13.7893 19.0391 14 18.5304 14 18C14 17.4696 13.7893 16.9609 13.4142 16.5858C13.0391 16.2107 12.5304 16 12 16C11.4696 16 10.9609 16.2107 10.5858 16.5858C10.2107 16.9609 10 17.4696 10 18Z"
    />
  </FxSvg>
);

export const FxSelectIcon = (props: FxSvgProps) => (
  <FxSvg width={24} height={24} viewBox="0 0 24 24" {...props}>
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M7 3.75C5.20507 3.75 3.75 5.20507 3.75 7V17C3.75 18.7949 5.20507 20.25 7 20.25H17C18.7949 20.25 20.25 18.7949 20.25 17V7C20.25 5.20507 18.7949 3.75 17 3.75H7ZM2.25 7C2.25 4.37665 4.37665 2.25 7 2.25H17C19.6234 2.25 21.75 4.37665 21.75 7V17C21.75 19.6234 19.6234 21.75 17 21.75H7C4.37665 21.75 2.25 19.6234 2.25 17V7Z"
    />
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M15.513 9.45285C15.8151 9.73615 15.8305 10.2108 15.5472 10.513L11.7972 14.513C11.5192 14.8095 11.0555 14.8306 10.7517 14.5606L8.50174 12.5606C8.19215 12.2854 8.16426 11.8113 8.43945 11.5017C8.71464 11.1921 9.18869 11.1643 9.49828 11.4394L11.2024 12.9542L14.4529 9.48705C14.7362 9.18486 15.2108 9.16955 15.513 9.45285Z"
    />
  </FxSvg>
);

export const FxSelectedIcon = (props: FxSvgProps) => (
  <FxSvg width={16} height={16} viewBox="0 0 16 16" fill="none" {...props}>
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M7 3.75C5.20507 3.75 3.75 5.20507 3.75 7V17C3.75 18.7949 5.20507 20.25 7 20.25H17C18.7949 20.25 20.25 18.7949 20.25 17V7C20.25 5.20507 18.7949 3.75 17 3.75H7ZM2.25 7C2.25 4.37665 4.37665 2.25 7 2.25H17C19.6234 2.25 21.75 4.37665 21.75 7V17C21.75 19.6234 19.6234 21.75 17 21.75H7C4.37665 21.75 2.25 19.6234 2.25 17V7Z"
      fill="#049B8F"
    />
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M6.1875 3.1875C4.53065 3.1875 3.1875 4.53065 3.1875 6.1875V17.8125C3.1875 19.4694 4.53065 20.8125 6.1875 20.8125H17.8125C19.4694 20.8125 20.8125 19.4694 20.8125 17.8125V6.1875C20.8125 4.53065 19.4694 3.1875 17.8125 3.1875H6.1875ZM15.5472 10.513C15.8305 10.2108 15.8151 9.73615 15.513 9.45285C15.2108 9.16955 14.7362 9.18486 14.4529 9.48705L11.2024 12.9542L9.49828 11.4394C9.18869 11.1643 8.71464 11.1921 8.43945 11.5017C8.16426 11.8113 8.19215 12.2854 8.50174 12.5606L10.7517 14.5606C11.0555 14.8306 11.5192 14.8095 11.7972 14.513L15.5472 10.513Z"
      fill="#049B8F"
    />
  </FxSvg>
);
export const FxArrowRightIcon = (props: FxSvgProps) => (
  <FxSvg width={24} height={24} viewBox="0 0 24 24" fill="none" {...props}>
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M11.4697 3.46967C11.7626 3.17678 12.2374 3.17678 12.5303 3.46967L20.5303 11.4697C20.8232 11.7626 20.8232 12.2374 20.5303 12.5303L12.5303 20.5303C12.2374 20.8232 11.7626 20.8232 11.4697 20.5303C11.1768 20.2374 11.1768 19.7626 11.4697 19.4697L18.1893 12.75H4C3.58579 12.75 3.25 12.4142 3.25 12C3.25 11.5858 3.58579 11.25 4 11.25H18.1893L11.4697 4.53033C11.1768 4.23744 11.1768 3.76256 11.4697 3.46967Z"
    />
  </FxSvg>
);
export const FxArrowLeftIcon = (props: FxSvgProps) => (
  <FxSvg width={24} height={24} viewBox="0 0 24 24" fill="none" {...props}>
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M12.5303 3.46967C12.2374 3.17678 11.7626 3.17678 11.4697 3.46967L3.46967 11.4697C3.17678 11.7626 3.17678 12.2374 3.46967 12.5303L11.4697 20.5303C11.7626 20.8232 12.2374 20.8232 12.5303 20.5303C12.8232 20.2374 12.8232 19.7626 12.5303 19.4697L5.81066 12.75H20C20.4142 12.75 20.75 12.4142 20.75 12C20.75 11.5858 20.4142 11.25 20 11.25H5.81066L12.5303 4.53033C12.8232 4.23744 12.8232 3.76256 12.5303 3.46967Z"
    />
  </FxSvg>
);
export const FxArrowUpIcon = (props: FxSvgProps) => (
  <FxSvg width={24} height={24} viewBox="0 0 24 24" fill="none" {...props}>
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M20.5303 12.5303C20.8232 12.2374 20.8232 11.7626 20.5303 11.4697L12.5303 3.46967C12.2374 3.17678 11.7626 3.17678 11.4697 3.46967L3.46967 11.4697C3.17678 11.7626 3.17678 12.2374 3.46967 12.5303C3.76256 12.8232 4.23744 12.8232 4.53033 12.5303L11.25 5.81066L11.25 20C11.25 20.4142 11.5858 20.75 12 20.75C12.4142 20.75 12.75 20.4142 12.75 20L12.75 5.81066L19.4697 12.5303C19.7626 12.8232 20.2374 12.8232 20.5303 12.5303Z"
    />
  </FxSvg>
);
export const FxArrowDownIcon = (props: FxSvgProps) => (
  <FxSvg width={24} height={24} viewBox="0 0 24 24" fill="none" {...props}>
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M20.5303 11.4697C20.8232 11.7626 20.8232 12.2374 20.5303 12.5303L12.5303 20.5303C12.2374 20.8232 11.7626 20.8232 11.4697 20.5303L3.46967 12.5303C3.17678 12.2374 3.17678 11.7626 3.46967 11.4697C3.76256 11.1768 4.23744 11.1768 4.53033 11.4697L11.25 18.1893L11.25 4C11.25 3.58579 11.5858 3.25 12 3.25C12.4142 3.25 12.75 3.58579 12.75 4L12.75 18.1893L19.4697 11.4697C19.7626 11.1768 20.2374 11.1768 20.5303 11.4697Z"
    />
  </FxSvg>
);

export const FxCopyIcon = (props: FxSvgProps) => (
  <FxSvg width={24} height={24} viewBox="0 0 24 24" fill="none" {...props}>
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M8.5 2.25C7.5335 2.25 6.75 3.0335 6.75 4V5.25H5.5C4.5335 5.25 3.75 6.0335 3.75 7V20C3.75 20.9665 4.5335 21.75 5.5 21.75H15.5C16.4665 21.75 17.25 20.9665 17.25 20V18.75H18.5C19.4665 18.75 20.25 17.9665 20.25 17V8.41421C20.25 7.95008 20.0656 7.50496 19.7374 7.17678L15.3232 2.76256C14.995 2.43437 14.5499 2.25 14.0858 2.25H8.5ZM15.75 18.75V20C15.75 20.1381 15.6381 20.25 15.5 20.25H5.5C5.36193 20.25 5.25 20.1381 5.25 20V7C5.25 6.86193 5.36193 6.75 5.5 6.75H6.75V17C6.75 17.9665 7.5335 18.75 8.5 18.75H15.75ZM8.25 6V17C8.25 17.1381 8.36193 17.25 8.5 17.25H16.5H18.5C18.6381 17.25 18.75 17.1381 18.75 17V8.41421C18.75 8.34791 18.7237 8.28432 18.6768 8.23744L14.2626 3.82322C14.2157 3.77634 14.1521 3.75 14.0858 3.75H8.5C8.36193 3.75 8.25 3.86193 8.25 4V6Z"
    />
  </FxSvg>
);

export const FxStarFilledIcon = (props: FxSvgProps) => (
  <FxSvg width={24} height={24} viewBox="0 0 24 24" fill="none" {...props}>
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M17.7177 20.3195C17.8557 20.779 17.3301 21.1502 16.9431 20.8665L12.1868 17.3787C12.0148 17.2526 11.7817 17.2494 11.6064 17.3709L6.75823 20.7299C6.36384 21.0031 5.84836 20.6179 5.99863 20.1623L7.84596 14.561C7.91277 14.3584 7.84371 14.1358 7.674 14.0066L2.98125 10.4336C2.59951 10.143 2.80655 9.53371 3.28634 9.53582L9.1844 9.56183C9.3977 9.56277 9.5881 9.42829 9.65855 9.22696L11.6064 3.65981C11.7649 3.20694 12.4083 3.21557 12.5546 3.67253L14.3524 9.28993C14.4175 9.49307 14.6042 9.63261 14.8174 9.63739L20.714 9.7696C21.1937 9.78035 21.3843 10.395 20.9949 10.6753L16.208 14.121C16.0349 14.2456 15.9599 14.4663 16.0213 14.6706L17.7177 20.3195Z"
    />
  </FxSvg>
);
export const FxStarOutlineIcon = (props: FxSvgProps) => (
  <FxSvg width={24} height={24} viewBox="0 0 24 24" fill="none" {...props}>
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M11.9241 15.326L15.4222 17.8912L14.1746 13.7366L17.6952 11.2023L13.3584 11.1051L12.0361 6.97361L10.6035 11.0681L6.26558 11.049L9.71701 13.6768L8.35835 17.7964L11.9241 15.326ZM6.7582 20.7299C6.36381 21.0032 5.84833 20.618 5.9986 20.1623L7.95757 14.2225L2.98122 10.4336C2.59948 10.143 2.80652 9.53372 3.28631 9.53584L9.5408 9.56341L11.6064 3.65982C11.7649 3.20695 12.4083 3.21559 12.5546 3.67254L14.4611 9.62941L20.714 9.76961C21.1937 9.78037 21.3843 10.395 20.9949 10.6753L15.9187 14.3293L17.6747 20.1765C17.8265 20.682 17.2483 21.0903 16.8227 20.7782L11.8994 17.1679L6.7582 20.7299Z"
    />
  </FxSvg>
);
export const FxScanCodeIcon = (props: FxSvgProps) => (
  <FxSvg width={24} height={24} viewBox="0 0 24 24" fill="none" {...props}>
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M4 3.75C3.86193 3.75 3.75 3.86193 3.75 4V8C3.75 8.41421 3.41421 8.75 3 8.75C2.58579 8.75 2.25 8.41421 2.25 8V4C2.25 3.0335 3.0335 2.25 4 2.25H8C8.41421 2.25 8.75 2.58579 8.75 3C8.75 3.41421 8.41421 3.75 8 3.75H4ZM20 3.75C20.1381 3.75 20.25 3.86193 20.25 4V8C20.25 8.41421 20.5858 8.75 21 8.75C21.4142 8.75 21.75 8.41421 21.75 8V4C21.75 3.0335 20.9665 2.25 20 2.25H16C15.5858 2.25 15.25 2.58579 15.25 3C15.25 3.41421 15.5858 3.75 16 3.75H20ZM20.25 20C20.25 20.1381 20.1381 20.25 20 20.25H16C15.5858 20.25 15.25 20.5858 15.25 21C15.25 21.4142 15.5858 21.75 16 21.75H20C20.9665 21.75 21.75 20.9665 21.75 20V16C21.75 15.5858 21.4142 15.25 21 15.25C20.5858 15.25 20.25 15.5858 20.25 16V20ZM4 20.25C3.86193 20.25 3.75 20.1381 3.75 20V16C3.75 15.5858 3.41421 15.25 3 15.25C2.58579 15.25 2.25 15.5858 2.25 16V20C2.25 20.9665 3.0335 21.75 4 21.75H8C8.41421 21.75 8.75 21.4142 8.75 21C8.75 20.5858 8.41421 20.25 8 20.25H4ZM3 11.25C2.58579 11.25 2.25 11.5858 2.25 12C2.25 12.4142 2.58579 12.75 3 12.75H21C21.4142 12.75 21.75 12.4142 21.75 12C21.75 11.5858 21.4142 11.25 21 11.25H3Z"
    />
  </FxSvg>
);
export const FxSettingsIcon = (props: FxSvgProps) => (
  <FxSvg width={24} height={24} viewBox="0 0 24 24" fill="none" {...props}>
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M10.5 6C10.5 6.82843 9.82843 7.5 9 7.5C8.17157 7.5 7.5 6.82843 7.5 6C7.5 5.17157 8.17157 4.5 9 4.5C9.82843 4.5 10.5 5.17157 10.5 6ZM11.9055 6.75C11.5725 8.04392 10.3979 9 9 9C7.60212 9 6.42755 8.04392 6.09451 6.75H4C3.58579 6.75 3.25 6.41421 3.25 6C3.25 5.58579 3.58579 5.25 4 5.25H6.09451C6.42755 3.95608 7.60212 3 9 3C10.3979 3 11.5725 3.95608 11.9055 5.25H20C20.4142 5.25 20.75 5.58579 20.75 6C20.75 6.41421 20.4142 6.75 20 6.75H11.9055ZM4 11.25C3.58579 11.25 3.25 11.5858 3.25 12C3.25 12.4142 3.58579 12.75 4 12.75H13.0945C13.4275 14.0439 14.6021 15 16 15C17.3979 15 18.5725 14.0439 18.9055 12.75H20C20.4142 12.75 20.75 12.4142 20.75 12C20.75 11.5858 20.4142 11.25 20 11.25H18.9055C18.5725 9.95608 17.3979 9 16 9C14.6021 9 13.4275 9.95608 13.0945 11.25H4ZM3.25 18C3.25 17.5858 3.58579 17.25 4 17.25H5.09451C5.42755 15.9561 6.60212 15 8 15C9.39788 15 10.5725 15.9561 10.9055 17.25H20C20.4142 17.25 20.75 17.5858 20.75 18C20.75 18.4142 20.4142 18.75 20 18.75H10.9055C10.5725 20.0439 9.39788 21 8 21C6.60212 21 5.42755 20.0439 5.09451 18.75H4C3.58579 18.75 3.25 18.4142 3.25 18ZM16 13.5C16.8284 13.5 17.5 12.8284 17.5 12C17.5 11.1716 16.8284 10.5 16 10.5C15.1716 10.5 14.5 11.1716 14.5 12C14.5 12.8284 15.1716 13.5 16 13.5ZM9.5 18C9.5 18.8284 8.82843 19.5 8 19.5C7.17157 19.5 6.5 18.8284 6.5 18C6.5 17.1716 7.17157 16.5 8 16.5C8.82843 16.5 9.5 17.1716 9.5 18Z"
    />
  </FxSvg>
);

export const FxPlugIcon = (props: FxSvgProps) => (
  <FxSvg width={24} height={24} viewBox="0 0 24 24" fill="none" {...props}>
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M21.78 3.27997C21.9125 3.13779 21.9846 2.94975 21.9812 2.75545C21.9777 2.56114 21.899 2.37576 21.7616 2.23835C21.6242 2.10093 21.4388 2.02222 21.2445 2.01879C21.0502 2.01537 20.8622 2.08749 20.72 2.21997L18.708 4.23197C17.8893 3.65188 16.8919 3.38007 15.8921 3.46462C14.8923 3.54917 13.9546 3.98463 13.245 4.69397L12.177 5.76297C11.8491 6.09112 11.6649 6.53605 11.6649 6.99997C11.6649 7.46388 11.8491 7.90882 12.177 8.23697L15.762 11.823C15.9245 11.9855 16.1174 12.1144 16.3298 12.2024C16.5421 12.2903 16.7697 12.3356 16.9995 12.3356C17.2293 12.3356 17.4569 12.2903 17.6692 12.2024C17.8816 12.1144 18.0745 11.9855 18.237 11.823L19.305 10.755C20.0145 10.0455 20.4501 9.10787 20.5349 8.10808C20.6196 7.10829 20.348 6.11075 19.768 5.29197L21.78 3.27997ZM18.195 5.75497L18.217 5.77797L18.22 5.77997L18.222 5.78297L18.245 5.80497C18.5005 6.06035 18.7032 6.36356 18.8415 6.69729C18.9797 7.03102 19.0509 7.38872 19.0509 7.74997C19.0509 8.11121 18.9797 8.46892 18.8415 8.80265C18.7032 9.13638 18.5005 9.43959 18.245 9.69497L17.177 10.762C17.1538 10.7853 17.1262 10.8037 17.0958 10.8163C17.0654 10.8289 17.0329 10.8354 17 10.8354C16.9671 10.8354 16.9346 10.8289 16.9042 10.8163C16.8738 10.8037 16.8462 10.7853 16.823 10.762L13.237 7.17697C13.2137 7.15375 13.1952 7.12616 13.1826 7.09579C13.17 7.06541 13.1636 7.03285 13.1636 6.99997C13.1636 6.96708 13.17 6.93452 13.1826 6.90415C13.1952 6.87378 13.2137 6.84619 13.237 6.82297L14.305 5.75497C14.5604 5.49947 14.8636 5.29679 15.1973 5.15851C15.5311 5.02023 15.8888 4.94906 16.25 4.94906C16.6112 4.94906 16.9689 5.02023 17.3027 5.15851C17.6364 5.29679 17.9396 5.49947 18.195 5.75497ZM10.78 11.28C10.8537 11.2113 10.9128 11.1285 10.9538 11.0365C10.9948 10.9445 11.0168 10.8452 11.0186 10.7445C11.0204 10.6438 11.0018 10.5438 10.9641 10.4504C10.9264 10.357 10.8703 10.2721 10.799 10.2009C10.7278 10.1297 10.643 10.0736 10.5496 10.0358C10.4562 9.99812 10.3562 9.9796 10.2555 9.98138C10.1548 9.98315 10.0555 10.0052 9.96346 10.0462C9.87146 10.0872 9.78866 10.1463 9.72 10.22L8 11.94L7.53 11.47C7.38938 11.3295 7.19875 11.2506 7 11.2506C6.80125 11.2506 6.61063 11.3295 6.47 11.47L4.695 13.245C3.98551 13.9545 3.54986 14.8921 3.46513 15.8919C3.38039 16.8916 3.65204 17.8892 4.232 18.708L2.22 20.72C2.14631 20.7886 2.08721 20.8714 2.04622 20.9634C2.00523 21.0554 1.98319 21.1547 1.98141 21.2554C1.97963 21.3561 1.99816 21.4562 2.03588 21.5496C2.0736 21.643 2.12974 21.7278 2.20096 21.799C2.27218 21.8702 2.35702 21.9264 2.4504 21.9641C2.54379 22.0018 2.64382 22.0203 2.74452 22.0186C2.84523 22.0168 2.94454 21.9947 3.03654 21.9538C3.12854 21.9128 3.21134 21.8537 3.28 21.78L5.292 19.768C6.11079 20.3479 7.10832 20.6196 8.10812 20.5348C9.10791 20.4501 10.0455 20.0145 10.755 19.305L12.53 17.53C12.6705 17.3893 12.7493 17.1987 12.7493 17C12.7493 16.8012 12.6705 16.6106 12.53 16.47L12.06 16L13.78 14.28C13.8537 14.2113 13.9128 14.1285 13.9538 14.0365C13.9948 13.9445 14.0168 13.8452 14.0186 13.7445C14.0204 13.6438 14.0018 13.5438 13.9641 13.4504C13.9264 13.357 13.8703 13.2721 13.799 13.2009C13.7278 13.1297 13.643 13.0736 13.5496 13.0358C13.4562 12.9981 13.3562 12.9796 13.2555 12.9814C13.1548 12.9832 13.0555 13.0052 12.9635 13.0462C12.8715 13.0872 12.7887 13.1463 12.72 13.22L11 14.94L9.06 13L10.78 11.28ZM7.466 13.527L7.47 13.53L7.473 13.534L10.466 16.527L10.47 16.53L10.473 16.534L10.939 17L9.695 18.245C9.43962 18.5005 9.13641 18.7031 8.80268 18.8414C8.46895 18.9797 8.11124 19.0509 7.75 19.0509C7.38876 19.0509 7.03105 18.9797 6.69732 18.8414C6.36359 18.7031 6.06038 18.5005 5.805 18.245L5.755 18.195C5.4995 17.9396 5.29682 17.6364 5.15854 17.3026C5.02026 16.9689 4.94909 16.6112 4.94909 16.25C4.94909 15.8887 5.02026 15.531 5.15854 15.1973C5.29682 14.8636 5.4995 14.5603 5.755 14.305L7 13.062L7.466 13.528V13.527Z"
    />
  </FxSvg>
);
export const FxUserIcon = (props: FxSvgProps) => (
  <FxSvg width={24} height={24} viewBox="0 0 24 24" fill="none" {...props}>
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M14.5 7.125C14.5 8.50571 13.3807 9.625 12 9.625C10.6193 9.625 9.49997 8.50571 9.49997 7.125C9.49997 5.74429 10.6193 4.625 12 4.625C13.3807 4.625 14.5 5.74429 14.5 7.125ZM16 7.125C16 9.33414 14.2091 11.125 12 11.125C9.79083 11.125 7.99997 9.33414 7.99997 7.125C7.99997 4.91586 9.79083 3.125 12 3.125C14.2091 3.125 16 4.91586 16 7.125ZM12 12.375C7.50285 12.375 5.01978 14.247 3.69189 16.2039C3.04076 17.1634 2.6831 18.1191 2.48757 18.8332C2.38951 19.1913 2.3313 19.4921 2.2973 19.7071C2.28028 19.8147 2.26926 19.9011 2.26234 19.963C2.25888 19.9939 2.25644 20.0187 2.25478 20.0369L2.25285 20.0593L2.25227 20.0667L2.25206 20.0695L2.25198 20.0706C2.25194 20.0711 2.25191 20.0716 3 20.125L2.25191 20.0716C2.2224 20.4847 2.53341 20.8436 2.94657 20.8731C3.3592 20.9026 3.71767 20.5924 3.74798 20.18C3.748 20.1798 3.74802 20.1795 3.74804 20.1792L3.74861 20.1729C3.74936 20.1646 3.75076 20.1501 3.75304 20.1298C3.75759 20.0891 3.76563 20.0252 3.77888 19.9414C3.80542 19.7736 3.85268 19.5275 3.93431 19.2293C4.09816 18.6309 4.39674 17.8366 4.93311 17.0461C5.98022 15.503 7.99715 13.875 12 13.875C16.0028 13.875 18.0198 15.503 19.0669 17.0461C19.6033 17.8366 19.9018 18.6309 20.0657 19.2293C20.1473 19.5275 20.1946 19.7736 20.2211 19.9414C20.2344 20.0252 20.2424 20.0891 20.247 20.1298C20.2492 20.1501 20.2506 20.1646 20.2514 20.1729L20.2519 20.1784L20.252 20.1792L20.252 20.1798L20.252 20.1799L20.252 20.18L20.2521 20.1805C20.2826 20.5926 20.641 20.9026 21.0534 20.8731C21.4666 20.8436 21.7776 20.4847 21.7481 20.0716L21 20.125C21.7481 20.0716 21.7481 20.0711 21.748 20.0706L21.7479 20.0695L21.7477 20.0667L21.7472 20.0593L21.7452 20.0369C21.7436 20.0187 21.7411 19.9939 21.7377 19.963C21.7307 19.9011 21.7197 19.8147 21.7027 19.7071C21.6687 19.4921 21.6105 19.1913 21.5124 18.8332C21.3169 18.1191 20.9592 17.1634 20.3081 16.2039C18.9802 14.247 16.4972 12.375 12 12.375Z"
    />
  </FxSvg>
);
export const FxRewardIcon = (props: FxSvgProps) => (
  <FxSvg width={24} height={24} viewBox="0 0 24 24" fill="none" {...props}>
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M18.7788 5.08654C17.6154 3.09615 16.0288 2 14.3077 2H9.69231C7.97115 2 6.38461 3.09615 5.22115 5.08654C4.05769 7.07692 3.53846 9.40385 3.53846 12C3.53846 14.5962 4.13461 17.0577 5.22115 18.9135C6.30769 20.7692 7.97115 22 9.69231 22H14.3077C16.0288 22 17.6154 20.9038 18.7788 18.9135C19.9423 16.9231 20.4615 14.5962 20.4615 12C20.4615 9.40385 19.8654 6.94231 18.7788 5.08654ZM18.9038 11.2308H15.8269C15.75 9.57692 15.4327 7.99038 14.8942 6.61538H17.8365C18.4231 7.90385 18.8173 9.5 18.9038 11.2308ZM16.9231 5.07692H14.1635C13.8379 4.52238 13.4508 4.0063 13.0096 3.53846H14.3077C15.2692 3.53846 16.1731 4.11538 16.9231 5.07692ZM5.07692 12C5.07692 7.41346 7.19231 3.53846 9.69231 3.53846C12.1923 3.53846 14.3077 7.41346 14.3077 12C14.3077 16.5865 12.1923 20.4615 9.69231 20.4615C7.19231 20.4615 5.07692 16.5865 5.07692 12ZM14.3077 20.4615H13.0096C13.4508 19.9937 13.8379 19.4776 14.1635 18.9231H16.9231C16.1731 19.8846 15.2692 20.4615 14.3077 20.4615ZM17.8365 17.3846H14.8942C15.4327 16.0096 15.75 14.4231 15.8269 12.7692H18.9038C18.8173 14.5 18.4231 16.0962 17.8365 17.3846Z"
    />
  </FxSvg>
);
export const FxBloxIcon = (props: FxSvgProps) => (
  <FxSvg width={24} height={24} viewBox="0 0 24 24" fill="none" {...props}>
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M4 3.5H9C9.27614 3.5 9.5 3.72386 9.5 4V9C9.5 9.27614 9.27614 9.5 9 9.5H4C3.72386 9.5 3.5 9.27614 3.5 9V4C3.5 3.72386 3.72386 3.5 4 3.5ZM2 4C2 2.89543 2.89543 2 4 2H9C10.1046 2 11 2.89543 11 4V9C11 10.1046 10.1046 11 9 11H4C2.89543 11 2 10.1046 2 9V4ZM15 3.5H20C20.2761 3.5 20.5 3.72386 20.5 4V9C20.5 9.27614 20.2761 9.5 20 9.5H15C14.7239 9.5 14.5 9.27614 14.5 9V4C14.5 3.72386 14.7239 3.5 15 3.5ZM13 4C13 2.89543 13.8954 2 15 2H20C21.1046 2 22 2.89543 22 4V9C22 10.1046 21.1046 11 20 11H15C13.8954 11 13 10.1046 13 9V4ZM9 14.5H4C3.72386 14.5 3.5 14.7239 3.5 15V20C3.5 20.2761 3.72386 20.5 4 20.5H9C9.27614 20.5 9.5 20.2761 9.5 20V15C9.5 14.7239 9.27614 14.5 9 14.5ZM4 13C2.89543 13 2 13.8954 2 15V20C2 21.1046 2.89543 22 4 22H9C10.1046 22 11 21.1046 11 20V15C11 13.8954 10.1046 13 9 13H4ZM13 15.05C13 13.9178 13.9178 13 15.05 13H21.25C21.6642 13 22 13.3358 22 13.75C22 14.1642 21.6642 14.5 21.25 14.5H15.5607L21.7803 20.7197C22.0732 21.0126 22.0732 21.4874 21.7803 21.7803C21.4874 22.0732 21.0126 22.0732 20.7197 21.7803L14.5 15.5607V21.25C14.5 21.6642 14.1642 22 13.75 22C13.3358 22 13 21.6642 13 21.25V15.05Z"
    />
  </FxSvg>
);
export const FxTrashIcon = (props: FxSvgProps) => (
  <FxSvg width={24} height={24} viewBox="0 0 24 24" fill="none" {...props}>
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M9.63219 3.58594C9.19317 3.58594 8.78634 3.81625 8.56041 4.19267L7.62103 5.75781H16.379L15.4396 4.19267C15.2136 3.81625 14.8068 3.58594 14.3678 3.58594H9.63219ZM7.27428 3.42074L5.8716 5.75781H2.78125C2.36704 5.75781 2.03125 6.0936 2.03125 6.50781C2.03125 6.92203 2.36704 7.25781 2.78125 7.25781H4.38063L5.21721 19.8137C5.29596 20.9956 6.27765 21.9141 7.46223 21.9141H16.5378C17.7224 21.9141 18.7041 20.9956 18.7829 19.8137L19.6194 7.25781H21.2188C21.633 7.25781 21.9688 6.92203 21.9688 6.50781C21.9688 6.0936 21.633 5.75781 21.2188 5.75781H18.1284L16.7257 3.42074C16.2287 2.59262 15.3336 2.08594 14.3678 2.08594H9.63219C8.66635 2.08594 7.77131 2.59262 7.27428 3.42074ZM18.1161 7.25781H5.88396L6.71389 19.714C6.74014 20.1079 7.06737 20.4141 7.46223 20.4141H16.5378C16.9327 20.4141 17.2599 20.1079 17.2862 19.714L18.1161 7.25781ZM10.1719 10.3828C10.5861 10.3828 10.9219 10.7186 10.9219 11.1328V15.7422C10.9219 16.1564 10.5861 16.4922 10.1719 16.4922C9.75766 16.4922 9.42188 16.1564 9.42188 15.7422V11.1328C9.42188 10.7186 9.75766 10.3828 10.1719 10.3828ZM13.8281 10.3828C14.2423 10.3828 14.5781 10.7186 14.5781 11.1328V15.7422C14.5781 16.1564 14.2423 16.4922 13.8281 16.4922C13.4139 16.4922 13.0781 16.1564 13.0781 15.7422V11.1328C13.0781 10.7186 13.4139 10.3828 13.8281 10.3828Z"
    />
  </FxSvg>
);
export const FxRenameIcon = (props: FxSvgProps) => (
  <FxSvg width={24} height={24} viewBox="0 0 24 24" fill="none" {...props}>
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M15.3008 6.1403L17.1441 4.2969C17.4853 3.97309 17.9392 3.79477 18.4098 3.79991C18.8829 3.80508 19.3351 3.99528 19.6697 4.32976C20.0043 4.66425 20.1946 5.11645 20.1999 5.58953C20.2052 6.06007 20.027 6.51406 19.7033 6.85535L17.8595 8.69899L15.3008 6.1403ZM14.2402 7.201L4.91341 16.5285L4.38324 15.9984L4.91337 16.5286C4.73886 16.7031 4.6199 16.9253 4.5715 17.1673L4.57149 17.1673L4.00614 19.9941L6.83194 19.4288L6.83196 19.4288C7.07395 19.3804 7.29621 19.2614 7.47071 19.0869L7.47075 19.0869L16.7988 9.7596L14.2402 7.201ZM3.85275 15.4679L3.85271 15.4679C3.4688 15.8518 3.20712 16.3408 3.10063 16.8731L3.10063 16.8732L2.31463 20.8032C2.26545 21.0491 2.34242 21.3033 2.51974 21.4806C2.69707 21.6579 2.95128 21.7349 3.19718 21.6857L7.12616 20.8997L7.12618 20.8997C7.65857 20.7932 8.14752 20.5315 8.53141 20.1475L20.7704 7.90959L20.7821 7.89763C21.3801 7.27238 21.7095 6.43785 21.6998 5.57272C21.6901 4.70759 21.3421 3.88065 20.7302 3.26896C20.1183 2.65727 19.2913 2.30946 18.4262 2.3C17.561 2.29055 16.7266 2.6202 16.1015 3.21837L16.0897 3.22993L3.85275 15.4679Z"
    />
  </FxSvg>
);
export const FxShareIcon = (props: FxSvgProps) => (
  <FxSvg width={24} height={24} viewBox="0 0 24 24" fill="none" {...props}>
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M11.4186 3.5625C9.29573 3.5625 7.57483 5.28341 7.57483 7.40625C7.57483 9.52909 9.29573 11.25 11.4186 11.25C13.5414 11.25 15.2623 9.52909 15.2623 7.40625C15.2623 5.28341 13.5414 3.5625 11.4186 3.5625ZM6.07483 7.40625C6.07483 4.45498 8.46731 2.0625 11.4186 2.0625C14.3699 2.0625 16.7623 4.45498 16.7623 7.40625C16.7623 10.3575 14.3699 12.75 11.4186 12.75C8.46731 12.75 6.07483 10.3575 6.07483 7.40625Z"
    />
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M17.8499 12.1687C18.2641 12.1687 18.5999 12.5045 18.5999 12.9187V14.925H20.6061C21.0204 14.925 21.3561 15.2607 21.3561 15.675C21.3561 16.0892 21.0204 16.425 20.6061 16.425H18.5999V18.4312C18.5999 18.8454 18.2641 19.1812 17.8499 19.1812C17.4357 19.1812 17.0999 18.8454 17.0999 18.4312V16.425H15.0936C14.6794 16.425 14.3436 16.0892 14.3436 15.675C14.3436 15.2607 14.6794 14.925 15.0936 14.925H17.0999V12.9187C17.0999 12.5045 17.4357 12.1687 17.8499 12.1687ZM6.50212 14.5875C6.01298 14.5873 5.54062 14.7658 5.17387 15.0894C4.80714 15.4131 4.57126 15.8595 4.51059 16.3448C4.51059 16.3449 4.5106 16.3448 4.51059 16.3448L4.15228 19.215C4.13316 19.368 4.14679 19.5235 4.19229 19.6708C4.23779 19.8182 4.31412 19.9542 4.41619 20.0698C4.51827 20.1854 4.64376 20.2779 4.78435 20.3413C4.92493 20.4047 5.07739 20.4375 5.23161 20.4375L16.0124 20.4375C16.4266 20.4375 16.7624 20.7732 16.7624 21.1875C16.7624 21.6017 16.4266 21.9375 16.0124 21.9375H5.23194C5.23188 21.9375 5.23199 21.9375 5.23194 21.9375C4.86506 21.9375 4.50221 21.8595 4.16776 21.7087C3.83326 21.5579 3.53467 21.3377 3.2918 21.0626C3.04893 20.7876 2.86734 20.464 2.75907 20.1134C2.65081 19.7628 2.61835 19.3933 2.66383 19.0292C2.66384 19.0292 2.66383 19.0292 2.66383 19.0292L3.02215 16.159C3.12815 15.3108 3.5404 14.5304 4.18136 13.9647C4.82225 13.3992 5.64766 13.0872 6.50241 13.0875C6.50245 13.0875 6.50237 13.0875 6.50241 13.0875H6.82489C7.2391 13.0875 7.57489 13.4232 7.57489 13.8375C7.57489 14.2517 7.2391 14.5875 6.82489 14.5875H6.50212Z"
    />
  </FxSvg>
);
export const FxMoveIcon = (props: FxSvgProps) => (
  <FxSvg width={24} height={24} viewBox="0 0 24 24" fill="none" {...props}>
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M10.3687 3.29502C9.95715 3.04071 9.48285 2.90609 8.99904 2.90625H4.58334C3.89267 2.90625 3.23029 3.18062 2.74191 3.66899C2.25354 4.15737 1.97917 4.81975 1.97917 5.51042V18.4896C1.97917 19.1803 2.25354 19.8426 2.74191 20.331C3.23029 20.8194 3.89267 21.0938 4.58334 21.0938H19.4167C20.1073 21.0938 20.7697 20.8194 21.2581 20.331C21.7465 19.8426 22.0208 19.1803 22.0208 18.4896V9.21875C22.0208 8.52808 21.7465 7.8657 21.2581 7.37733C20.7697 6.88895 20.1073 6.61458 19.4167 6.61458H13.1459C12.9408 6.61457 12.7398 6.55744 12.5653 6.4496C12.3909 6.34175 12.2498 6.18728 12.1581 6.00382L11.3293 4.34619C11.113 3.91337 10.7804 3.54937 10.3687 3.29502ZM8.99939 4.40625C9.20457 4.40615 9.40573 4.46324 9.58029 4.57109C9.75485 4.67895 9.89598 4.83347 9.9877 5.01701L10.8165 6.67464C11.0327 7.10727 11.3652 7.47114 11.7765 7.72547C12.188 7.97983 12.6622 8.11457 13.1459 8.11458H19.4167C19.7095 8.11458 19.9904 8.23091 20.1974 8.43799C20.4045 8.64506 20.5208 8.92591 20.5208 9.21875V18.4896C20.5208 18.7824 20.4045 19.0633 20.1974 19.2703C19.9904 19.4774 19.7095 19.5938 19.4167 19.5938H4.58334C4.2905 19.5938 4.00965 19.4774 3.80257 19.2703C3.5955 19.0633 3.47917 18.7824 3.47917 18.4896V5.51042C3.47917 5.21757 3.5955 4.93672 3.80257 4.72965C4.00965 4.52258 4.29049 4.40625 4.58334 4.40625H8.99939ZM13.0028 10.5322C13.2957 10.2393 13.7706 10.2393 14.0635 10.5322L16.5068 12.9755C16.9949 13.4636 16.9949 14.2551 16.5068 14.7433L14.0635 17.1866C13.7706 17.4795 13.2957 17.4795 13.0028 17.1866C12.7099 16.8937 12.7099 16.4188 13.0028 16.1259L14.5193 14.6094L7.87688 14.6094C7.46266 14.6094 7.12688 14.2736 7.12688 13.8594C7.12688 13.4452 7.46266 13.1094 7.87688 13.1094L14.5193 13.1094L13.0028 11.5928C12.7099 11.2999 12.7099 10.8251 13.0028 10.5322Z"
    />
  </FxSvg>
);
export const FxCreateFolderIcon = (props: FxSvgProps) => (
  <FxSvg width={24} height={24} viewBox="0 0 24 24" fill="none" {...props}>
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M8.99904 2.90625C9.48285 2.90609 9.95715 3.04071 10.3687 3.29502C10.7804 3.54937 11.113 3.91337 11.3293 4.34619C11.3294 4.34625 11.3293 4.34614 11.3293 4.34619L12.1581 6.00382C12.2498 6.18728 12.3909 6.34175 12.5653 6.4496C12.7398 6.55744 12.9408 6.61457 13.1459 6.61458C13.1459 6.61458 13.1459 6.61458 13.1459 6.61458H19.4167C20.1073 6.61458 20.7697 6.88895 21.2581 7.37733C21.7465 7.8657 22.0208 8.52808 22.0208 9.21875V18.4896C22.0208 19.1803 21.7465 19.8426 21.2581 20.331C20.7697 20.8194 20.1073 21.0938 19.4167 21.0938H4.58334C3.89267 21.0938 3.23029 20.8194 2.74191 20.331C2.25354 19.8426 1.97917 19.1803 1.97917 18.4896V5.51042C1.97917 4.81975 2.25354 4.15737 2.74191 3.66899C3.23029 3.18062 3.89267 2.90625 4.58334 2.90625H8.99904C8.99902 2.90625 8.99906 2.90625 8.99904 2.90625ZM9.58029 4.57109C9.40573 4.46324 9.20457 4.40615 8.99939 4.40625H4.58334C4.29049 4.40625 4.00965 4.52258 3.80257 4.72965C3.5955 4.93672 3.47917 5.21757 3.47917 5.51042V18.4896C3.47917 18.7824 3.5955 19.0633 3.80257 19.2703C4.00965 19.4774 4.2905 19.5938 4.58334 19.5938H19.4167C19.7095 19.5938 19.9904 19.4774 20.1974 19.2703C20.4045 19.0633 20.5208 18.7824 20.5208 18.4896V9.21875C20.5208 8.92591 20.4045 8.64506 20.1974 8.43799C19.9904 8.23091 19.7095 8.11458 19.4167 8.11458H13.1459C12.6622 8.11457 12.188 7.97983 11.7765 7.72547C11.3652 7.47114 11.0327 7.10727 10.8165 6.67464C10.8165 6.67459 10.8165 6.67469 10.8165 6.67464L9.9877 5.01701C9.89598 4.83347 9.75485 4.67895 9.58029 4.57109Z"
    />
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M12.75 11.2705C12.75 10.8563 12.4142 10.5205 12 10.5205C11.5858 10.5205 11.25 10.8563 11.25 11.2705V12.8721H9.64844C9.23422 12.8721 8.89844 13.2079 8.89844 13.6221C8.89844 14.0363 9.23422 14.3721 9.64844 14.3721H11.25V15.9736C11.25 16.3878 11.5858 16.7236 12 16.7236C12.4142 16.7236 12.75 16.3878 12.75 15.9736V14.3721H14.3516C14.7658 14.3721 15.1016 14.0363 15.1016 13.6221C15.1016 13.2079 14.7658 12.8721 14.3516 12.8721H12.75V11.2705Z"
    />
  </FxSvg>
);

export const FxEditPermissionsIcon = (props: FxSvgProps) => (
  <FxSvg width={24} height={24} viewBox="0 0 24 24" fill="none" {...props}>
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M9.52969 3.29465C9.15158 3.06103 8.71587 2.93736 8.27142 2.9375H4.36013C3.72564 2.9375 3.11713 3.18955 2.66848 3.63821C2.21982 4.08686 1.96777 4.69537 1.96777 5.32986V16.8264C1.96777 17.4609 2.21982 18.0694 2.66848 18.518C3.11713 18.9667 3.72564 19.2188 4.36013 19.2188H10.176C10.5902 19.2188 10.926 18.883 10.926 18.4688C10.926 18.0545 10.5902 17.7188 10.176 17.7188H4.36013C4.12347 17.7188 3.89649 17.6247 3.72914 17.4574C3.56179 17.29 3.46777 17.0631 3.46777 16.8264V5.32986C3.46777 5.09319 3.56179 4.86622 3.72914 4.69887C3.89649 4.53152 4.12347 4.4375 4.36013 4.4375H8.27177C8.4376 4.43742 8.60017 4.48355 8.74124 4.57072C8.88231 4.65789 8.99638 4.7828 9.07051 4.93114L9.80464 6.39941C10.0033 6.79684 10.3087 7.13111 10.6866 7.36475C11.0646 7.59843 11.5002 7.72221 11.9446 7.72222H17.499C17.7357 7.72222 17.9627 7.81624 18.13 7.98359C18.2974 8.15094 18.3914 8.37792 18.3914 8.61458C18.3914 9.0288 18.7272 9.36458 19.1414 9.36458C19.5556 9.36458 19.8914 9.0288 19.8914 8.61458C19.8914 7.98009 19.6393 7.37158 19.1907 6.92293C18.742 6.47427 18.1335 6.22222 17.499 6.22222H11.9446C11.7788 6.22221 11.6163 6.17604 11.4754 6.08889C11.3344 6.00172 11.2204 5.87686 11.1463 5.72859L10.4122 4.26032C10.2134 3.8627 9.90785 3.52831 9.52969 3.29465ZM17.2604 12C16.734 12 16.3073 12.4267 16.3073 12.9531V14.3438H18.2135V12.9531C18.2135 12.4267 17.7868 12 17.2604 12ZM14.8073 12.9531V14.3438H14.2385C13.272 14.3438 12.4885 15.1273 12.4885 16.0938V19.3125C12.4885 20.279 13.272 21.0625 14.2385 21.0625H20.2822C21.2487 21.0625 22.0322 20.279 22.0322 19.3125V16.0938C22.0322 15.1273 21.2487 14.3438 20.2822 14.3438H19.7135V12.9531C19.7135 11.5983 18.6152 10.5 17.2604 10.5C15.9056 10.5 14.8073 11.5983 14.8073 12.9531ZM14.2385 15.8438C14.1005 15.8438 13.9885 15.9557 13.9885 16.0938V19.3125C13.9885 19.4506 14.1005 19.5625 14.2385 19.5625H20.2822C20.4203 19.5625 20.5322 19.4506 20.5322 19.3125V16.0938C20.5322 15.9557 20.4203 15.8438 20.2822 15.8438H14.2385Z"
    />
  </FxSvg>
);
export const FxAvailableOfflineIcon = (props: FxSvgProps) => (
  <FxSvg width={24} height={24} viewBox="0 0 24 24" fill="none" {...props}>
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M12 20.5C16.6944 20.5 20.5 16.6944 20.5 12C20.5 7.30558 16.6944 3.5 12 3.5C7.30558 3.5 3.5 7.30558 3.5 12C3.5 16.6944 7.30558 20.5 12 20.5ZM12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22ZM15.3272 10.6417C15.6201 10.3488 15.6201 9.87388 15.3272 9.58099C15.0343 9.2881 14.5594 9.2881 14.2665 9.58099L12.75 11.0975L12.75 6.52912C12.75 6.21964 12.4142 5.96875 12 5.96875C11.5858 5.96875 11.25 6.21964 11.25 6.52912V11.0975L9.73346 9.58099C9.44056 9.2881 8.96569 9.2881 8.67279 9.58099C8.3799 9.87388 8.3799 10.3488 8.67279 10.6417L11.1161 13.085C11.6043 13.5731 12.3957 13.5731 12.8839 13.085L15.3272 10.6417ZM7.95312 15.375C7.53891 15.375 7.20312 15.7108 7.20312 16.125C7.20312 16.5392 7.53891 16.875 7.95312 16.875H16.0469C16.4611 16.875 16.7969 16.5392 16.7969 16.125C16.7969 15.7108 16.4611 15.375 16.0469 15.375H7.95312Z"
    />
  </FxSvg>
);
export const FxAddFileIcon = (props: FxSvgProps) => (
  <FxSvg width={24} height={24} viewBox="0 0 24 24" fill="none" {...props}>
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M6.70313 3.49243C6.01277 3.49243 5.45312 4.05208 5.45312 4.74243V19.2577C5.45312 19.9481 6.01277 20.5077 6.70312 20.5077H17.2969C17.9872 20.5077 18.5469 19.9481 18.5469 19.2577V9.92993H14.9219C13.4031 9.92993 12.1719 8.69871 12.1719 7.17993V3.49243H6.70313ZM13.6719 4.28271L17.8822 8.42993H14.9219C14.2315 8.42993 13.6719 7.87029 13.6719 7.17993V4.28271ZM3.95312 4.74243C3.95312 3.22365 5.18434 1.99243 6.70313 1.99243H12.3573C13.0795 1.99243 13.7726 2.27649 14.2871 2.78326L19.2267 7.64877C19.7514 8.16564 20.0469 8.8714 20.0469 9.60795V19.2577C20.0469 20.7765 18.8157 22.0077 17.2969 22.0077H6.70312C5.18434 22.0077 3.95312 20.7765 3.95312 19.2577V4.74243ZM12 10.9143C12.4142 10.9143 12.75 11.2501 12.75 11.6643V13.2659H14.3516C14.7658 13.2659 15.1016 13.6017 15.1016 14.0159C15.1016 14.4301 14.7658 14.7659 14.3516 14.7659H12.75V16.3674C12.75 16.7816 12.4142 17.1174 12 17.1174C11.5858 17.1174 11.25 16.7816 11.25 16.3674V14.7659H9.64844C9.23422 14.7659 8.89844 14.4301 8.89844 14.0159C8.89844 13.6017 9.23422 13.2659 9.64844 13.2659H11.25V11.6643C11.25 11.2501 11.5858 10.9143 12 10.9143Z"
    />
  </FxSvg>
);
export const FxUploadOfflineIcon = (props: FxSvgProps) => (
  <FxSvg width={24} height={24} viewBox="0 0 24 24" fill="none" {...props}>
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M8.67279 7.76951C8.3799 7.47662 8.3799 7.00174 8.67279 6.70885L11.1161 4.26553C11.6043 3.77738 12.3957 3.77737 12.8839 4.26553L15.3272 6.70885C15.6201 7.00174 15.6201 7.47662 15.3272 7.76951C15.0343 8.06241 14.5594 8.06241 14.2665 7.76951L12.75 6.25297L12.75 15.413C12.75 15.8272 12.4142 16.163 12 16.163C11.5858 16.163 11.25 15.8272 11.25 15.413L11.25 6.25297L9.73346 7.76951C9.44056 8.06241 8.96569 8.06241 8.67279 7.76951ZM4.25 15.3505C4.25 14.9363 3.91421 14.6005 3.5 14.6005C3.08579 14.6005 2.75 14.9363 2.75 15.3505V17.3505C2.75 18.8693 3.98122 20.1005 5.5 20.1005H18.5C20.0188 20.1005 21.25 18.8693 21.25 17.3505V15.3505C21.25 14.9363 20.9142 14.6005 20.5 14.6005C20.0858 14.6005 19.75 14.9363 19.75 15.3505V17.3505C19.75 18.0409 19.1904 18.6005 18.5 18.6005H5.5C4.80964 18.6005 4.25 18.0409 4.25 17.3505V15.3505Z"
    />
  </FxSvg>
);
export const FxRefreshIcon = (props: FxSvgProps) => (
  <FxSvg width={24} height={24} viewBox="0 0 24 24" fill="none" {...props}>
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M12 4.9375C8.00978 4.9375 4.9375 7.81082 4.9375 11.0625C4.9375 11.4767 4.60171 11.8125 4.1875 11.8125C3.77329 11.8125 3.4375 11.4767 3.4375 11.0625C3.4375 6.81418 7.36022 3.4375 12 3.4375C14.5802 3.4375 16.6955 4.63964 18.1326 5.78935C18.4793 6.06671 18.7901 6.34384 19.0625 6.60501V4.1875C19.0625 3.77329 19.3983 3.4375 19.8125 3.4375C20.2267 3.4375 20.5625 3.77329 20.5625 4.1875V8.5625C20.5625 8.97671 20.2267 9.3125 19.8125 9.3125H16.0625C15.6483 9.3125 15.3125 8.97671 15.3125 8.5625C15.3125 8.14829 15.6483 7.8125 16.0625 7.8125H18.1527C17.8805 7.54401 17.5599 7.25217 17.1955 6.96065C15.8983 5.92286 14.1073 4.9375 12 4.9375ZM19.8125 12.1875C20.2267 12.1875 20.5625 12.5233 20.5625 12.9375C20.5625 17.1858 16.6398 20.5625 12 20.5625C9.41975 20.5625 7.30455 19.3604 5.86742 18.2107C5.52071 17.9333 5.20986 17.6562 4.9375 17.395V19.8125C4.9375 20.2267 4.60171 20.5625 4.1875 20.5625C3.77329 20.5625 3.4375 20.2267 3.4375 19.8125V15.4375C3.4375 15.0233 3.77329 14.6875 4.1875 14.6875H7.9375C8.35171 14.6875 8.6875 15.0233 8.6875 15.4375C8.6875 15.8517 8.35171 16.1875 7.9375 16.1875H5.84735C6.11952 16.456 6.44007 16.7478 6.80446 17.0393C8.1017 18.0771 9.89275 19.0625 12 19.0625C15.9902 19.0625 19.0625 16.1892 19.0625 12.9375C19.0625 12.5233 19.3983 12.1875 19.8125 12.1875Z"
    />
  </FxSvg>
);

export const FxFilesIcon = (props: FxSvgProps) => (
  <FxSvg width={24} height={24} viewBox="0 0 24 24" fill="none" {...props}>
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M7 2.28125C6.58579 2.28125 6.25 2.61704 6.25 3.03125C6.25 3.44546 6.58579 3.78125 7 3.78125H17C17.4142 3.78125 17.75 3.44546 17.75 3.03125C17.75 2.61704 17.4142 2.28125 17 2.28125H7ZM4.25 6.82812C4.25 6.41391 4.58579 6.07812 5 6.07812H19C19.4142 6.07812 19.75 6.41391 19.75 6.82812C19.75 7.24234 19.4142 7.57812 19 7.57812H5C4.58579 7.57812 4.25 7.24234 4.25 6.82812ZM20 11.375H4C3.72386 11.375 3.5 11.5989 3.5 11.875V19.7188C3.5 19.9949 3.72386 20.2188 4 20.2188H20C20.2761 20.2188 20.5 19.9949 20.5 19.7188V11.875C20.5 11.5989 20.2761 11.375 20 11.375ZM4 9.875C2.89543 9.875 2 10.7704 2 11.875V19.7188C2 20.8233 2.89543 21.7188 4 21.7188H20C21.1046 21.7188 22 20.8233 22 19.7188V11.875C22 10.7704 21.1046 9.875 20 9.875H4Z"
    />
  </FxSvg>
);
export const FxPhoneIcon = (props: FxSvgProps) => (
  <FxSvg width={24} height={24} viewBox="0 0 24 24" fill="none" {...props}>
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M7.75 3.5H16.25C16.5261 3.5 16.75 3.72386 16.75 4V20C16.75 20.2761 16.5261 20.5 16.25 20.5H7.75C7.47386 20.5 7.25 20.2761 7.25 20V4C7.25 3.72386 7.47386 3.5 7.75 3.5ZM5.75 4C5.75 2.89543 6.64543 2 7.75 2H16.25C17.3546 2 18.25 2.89543 18.25 4V20C18.25 21.1046 17.3546 22 16.25 22H7.75C6.64543 22 5.75 21.1046 5.75 20V4ZM12 19.3438C12.6041 19.3438 13.0938 18.8541 13.0938 18.25C13.0938 17.6459 12.6041 17.1562 12 17.1562C11.3959 17.1562 10.9062 17.6459 10.9062 18.25C10.9062 18.8541 11.3959 19.3438 12 19.3438Z"
    />
  </FxSvg>
);
export const FxZonesIcon = (props: FxSvgProps) => (
  <FxSvg width={24} height={24} viewBox="0 0 24 24" fill="none" {...props}>
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M20.5 12C20.5 16.6944 16.6944 20.5 12 20.5C7.30558 20.5 3.5 16.6944 3.5 12C3.5 7.30558 7.30558 3.5 12 3.5C16.6944 3.5 20.5 7.30558 20.5 12ZM22 12C22 17.5228 17.5228 22 12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12ZM12 9.98438C13.1046 9.98438 14 9.08894 14 7.98438C14 6.87981 13.1046 5.98438 12 5.98438C10.8954 5.98438 10 6.87981 10 7.98438C10 9.08894 10.8954 9.98438 12 9.98438ZM16 16C17.1046 16 18 15.1046 18 14C18 12.8954 17.1046 12 16 12C14.8954 12 14 12.8954 14 14C14 15.1046 14.8954 16 16 16ZM10 14C10 15.1046 9.10457 16 8 16C6.89543 16 6 15.1046 6 14C6 12.8954 6.89543 12 8 12C9.10457 12 10 12.8954 10 14Z"
    />
  </FxSvg>
);
export const FxSearchIcon = (props: FxSvgProps) => (
  <FxSvg width={24} height={24} viewBox="0 0 24 24" fill="none" {...props}>
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M11.0296 3.53125C9.04092 3.53125 7.13368 4.32125 5.72747 5.72747C4.32125 7.13368 3.53125 9.04092 3.53125 11.0296C3.53125 13.0183 4.32125 14.9255 5.72747 16.3317C7.13368 17.738 9.04092 18.528 11.0296 18.528C13.0107 18.528 14.9109 17.744 16.3155 16.3479C16.3221 16.3408 16.3288 16.3339 16.3357 16.327C16.342 16.3207 16.3484 16.3145 16.3549 16.3085C17.7466 14.9045 18.528 13.0074 18.528 11.0296C18.528 9.04092 17.738 7.13368 16.3317 5.72747C14.9255 4.32125 13.0183 3.53125 11.0296 3.53125ZM17.9038 16.8362C19.2702 15.2187 20.028 13.1632 20.028 11.0296C20.028 8.64309 19.0799 6.35433 17.3924 4.66681C15.7049 2.97929 13.4161 2.03125 11.0296 2.03125C8.64309 2.03125 6.35433 2.97929 4.66681 4.66681C2.97929 6.35433 2.03125 8.64309 2.03125 11.0296C2.03125 13.4161 2.97929 15.7049 4.66681 17.3924C6.35433 19.0799 8.64309 20.028 11.0296 20.028C13.1665 20.028 15.225 19.2679 16.8437 17.8975L20.6879 21.7486C20.9806 22.0418 21.4555 22.0422 21.7486 21.7496C22.0418 21.4569 22.0422 20.982 21.7496 20.6889L17.9038 16.8362"
    />
  </FxSvg>
);
export const FxAppIcon = (props: FxSvgProps) => (
  <FxSvg width={24} height={24} viewBox="0 0 24 24" fill="none" {...props}>
    <Path d="M12 12H4.85716C4.47828 12 4.11492 11.8495 3.84701 11.5816C3.5791 11.3137 3.42859 10.9503 3.42859 10.5714V3.42857C3.42859 3.04969 3.5791 2.68633 3.84701 2.41842C4.11492 2.15051 4.47828 2 4.85716 2H12C12.3789 2 12.7423 2.15051 13.0102 2.41842C13.2781 2.68633 13.4286 3.04969 13.4286 3.42857V10.5714C13.4286 10.9503 13.2781 11.3137 13.0102 11.5816C12.7423 11.8495 12.3789 12 12 12ZM4.85716 3.42857V10.5714H12V3.42857H4.85716ZM19.1429 7.71429V10.5714H16.2857V7.71429H19.1429ZM19.1429 6.28571H16.2857C15.9068 6.28571 15.5435 6.43622 15.2756 6.70413C15.0077 6.97204 14.8572 7.33541 14.8572 7.71429V10.5714C14.8572 10.9503 15.0077 11.3137 15.2756 11.5816C15.5435 11.8495 15.9068 12 16.2857 12H19.1429C19.5218 12 19.8851 11.8495 20.153 11.5816C20.4209 11.3137 20.5714 10.9503 20.5714 10.5714V7.71429C20.5714 7.33541 20.4209 6.97204 20.153 6.70413C19.8851 6.43622 19.5218 6.28571 19.1429 6.28571ZM19.1429 16.2857V19.1429H16.2857V16.2857H19.1429ZM19.1429 14.8571H16.2857C15.9068 14.8571 15.5435 15.0077 15.2756 15.2756C15.0077 15.5435 14.8572 15.9068 14.8572 16.2857V19.1429C14.8572 19.5217 15.0077 19.8851 15.2756 20.153C15.5435 20.4209 15.9068 20.5714 16.2857 20.5714H19.1429C19.5218 20.5714 19.8851 20.4209 20.153 20.153C20.4209 19.8851 20.5714 19.5217 20.5714 19.1429V16.2857C20.5714 15.9068 20.4209 15.5435 20.153 15.2756C19.8851 15.0077 19.5218 14.8571 19.1429 14.8571ZM12 17.7143V20.5714H9.14287V17.7143H12ZM12 16.2857H9.14287C8.76399 16.2857 8.40063 16.4362 8.13272 16.7041C7.86481 16.972 7.7143 17.3354 7.7143 17.7143V20.5714C7.7143 20.9503 7.86481 21.3137 8.13272 21.5816C8.40063 21.8495 8.76399 22 9.14287 22H12C12.3789 22 12.7423 21.8495 13.0102 21.5816C13.2781 21.3137 13.4286 20.9503 13.4286 20.5714V17.7143C13.4286 17.3354 13.2781 16.972 13.0102 16.7041C12.7423 16.4362 12.3789 16.2857 12 16.2857Z" />
  </FxSvg>
);
export const FxOpenInIcon = (props: FxSvgProps) => (
  <FxSvg width={24} height={24} viewBox="0 0 24 24" fill="none" {...props}>
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M15.3986 2.98438H20.0156C20.5679 2.98438 21.0156 3.43209 21.0156 3.98438V8.60141C21.0156 9.49232 19.9385 9.93848 19.3085 9.30852L17.5147 7.51472L7.51469 17.5147L9.30852 19.3086C9.93848 19.9385 9.49232 21.0157 8.60141 21.0157H3.98438C3.43209 21.0157 2.98438 20.568 2.98438 20.0157V15.3986C2.98438 14.5077 4.06152 14.0616 4.69148 14.6915L6.45403 16.4541L16.4541 6.45406L14.6915 4.69148C14.0615 4.06152 14.5077 2.98438 15.3986 2.98438Z"
    />
  </FxSvg>
);
export const FxPinOutlineIcon = (props: FxSvgProps) => (
  <FxSvg width={24} height={24} viewBox="0 0 24 24" fill="none" {...props}>
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M10.8771 2.69587C10.7692 2.48029 10.5644 2.32968 10.3264 2.29107C10.0885 2.25245 9.84643 2.33058 9.67596 2.50105L2.50099 9.67602C2.33052 9.84649 2.25239 10.0885 2.291 10.3265C2.32962 10.5644 2.48023 10.7693 2.69581 10.8771L2.69694 10.8777L2.70205 10.8803L2.72411 10.8914C2.74396 10.9015 2.7739 10.9168 2.81288 10.937C2.89085 10.9773 3.00487 11.037 3.1466 11.1133C3.4304 11.2662 3.82355 11.4846 4.25982 11.7463C5.14733 12.2788 6.15695 12.9576 6.80597 13.6067C7.07588 13.8766 7.39099 14.3695 7.7193 15.0261C8.0395 15.6665 8.34306 16.4022 8.60717 17.1065C8.87055 17.8089 9.09061 18.4689 9.24495 18.954C9.32202 19.1962 9.38245 19.394 9.42345 19.5307C9.44395 19.599 9.45957 19.652 9.46997 19.6876L9.48161 19.7276L9.48441 19.7373L9.48503 19.7395C9.55804 19.995 9.76091 20.1932 10.0182 20.2598C10.2754 20.3265 10.5487 20.252 10.7366 20.0641L14.87 15.9307L20.4384 21.4991C20.7313 21.792 21.2062 21.792 21.4991 21.4991C21.792 21.2062 21.792 20.7314 21.4991 20.4385L15.9307 14.8701L20.0641 10.7367C20.252 10.5488 20.3264 10.2755 20.2598 10.0182C20.1931 9.76097 19.9953 9.55821 19.7398 9.4852L19.7372 9.48447L19.7275 9.48167L19.6875 9.47003C19.6519 9.45963 19.599 9.44401 19.5306 9.42351C19.394 9.38252 19.1962 9.32208 18.954 9.24501C18.4689 9.09067 17.8088 8.87061 17.1065 8.60723C16.4022 8.34312 15.6664 8.03956 15.0261 7.71936C14.3694 7.39105 13.8765 7.07594 13.6066 6.80603C12.9576 6.15701 12.2788 5.14739 11.7463 4.25988C11.4845 3.82361 11.2661 3.43046 11.1133 3.14666C11.037 3.00493 10.9772 2.89091 10.9369 2.81294C10.9168 2.77396 10.9015 2.74403 10.8914 2.72418L10.8802 2.70211L10.8776 2.697L10.8771 2.69587ZM18.1257 10.5537C17.68 10.4074 17.144 10.2233 16.5798 10.0117C15.8491 9.73771 15.0601 9.41346 14.3552 9.061C13.6665 8.71666 12.9935 8.31428 12.5459 7.86669C11.76 7.08072 11.0038 5.93784 10.46 5.03162C10.2963 4.75868 10.1488 4.50209 10.0223 4.27606L4.276 10.0223C4.50203 10.1488 4.75862 10.2963 5.03156 10.4601C5.93778 11.0038 7.08065 11.76 7.86663 12.546C8.31422 12.9936 8.7166 13.6666 9.06094 14.3553C9.4134 15.0602 9.73765 15.8492 10.0117 16.5798C10.2232 17.144 10.4073 17.6801 10.5536 18.1258L18.1257 10.5537Z"
    />
  </FxSvg>
);
export const FxPinFilledIcon = (props: FxSvgProps) => (
  <FxSvg width={24} height={24} viewBox="0 0 24 24" fill="none" {...props}>
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M10.8771 2.69587C10.7692 2.48029 10.5644 2.32968 10.3264 2.29107C10.0885 2.25245 9.84643 2.33058 9.67596 2.50105L2.50099 9.67602C2.33052 9.84649 2.25239 10.0885 2.291 10.3265C2.32962 10.5644 2.48023 10.7693 2.69581 10.8771L2.69694 10.8777L2.70205 10.8803L2.72411 10.8914C2.74396 10.9015 2.7739 10.9168 2.81288 10.937C2.89085 10.9773 3.00487 11.037 3.1466 11.1133C3.4304 11.2662 3.82355 11.4846 4.25982 11.7463C5.14733 12.2788 6.15695 12.9576 6.80597 13.6067C7.07588 13.8766 7.39099 14.3695 7.7193 15.0261C8.0395 15.6665 8.34306 16.4022 8.60717 17.1065C8.87055 17.8089 9.09061 18.4689 9.24495 18.954C9.32202 19.1962 9.38245 19.394 9.42345 19.5307C9.44395 19.599 9.45957 19.652 9.46997 19.6876L9.48161 19.7276L9.48441 19.7373L9.48503 19.7395C9.55804 19.995 9.76091 20.1932 10.0182 20.2598C10.2754 20.3265 10.5487 20.252 10.7366 20.0641L14.87 15.9307L20.4384 21.4991C20.7313 21.792 21.2062 21.792 21.4991 21.4991C21.792 21.2062 21.792 20.7314 21.4991 20.4385L15.9307 14.8701L20.0641 10.7367C20.252 10.5488 20.3264 10.2755 20.2598 10.0182C20.1931 9.76097 19.9953 9.55821 19.7398 9.4852L19.7372 9.48447L19.7275 9.48167L19.6875 9.47003C19.6519 9.45963 19.599 9.44401 19.5306 9.42351C19.394 9.38252 19.1962 9.32208 18.954 9.24501C18.4689 9.09067 17.8088 8.87061 17.1065 8.60723C16.4022 8.34312 15.6664 8.03956 15.0261 7.71936C14.3694 7.39105 13.8765 7.07594 13.6066 6.80603C12.9576 6.15701 12.2788 5.14739 11.7463 4.25988C11.4845 3.82361 11.2661 3.43046 11.1133 3.14666C11.037 3.00493 10.9772 2.89091 10.9369 2.81294C10.9168 2.77396 10.9015 2.74403 10.8914 2.72418L10.8802 2.70211L10.8776 2.697L10.8771 2.69587Z"
    />
  </FxSvg>
);
export const FxMergeIcon = (props: FxSvgProps) => (
  <FxSvg width={24} height={24} viewBox="0 0 24 24" fill="none" {...props}>
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M4.28133 12L9.34753 6.93379L10.9394 8.52566L8.17217 11.2929C7.78164 11.6834 7.78164 12.3166 8.17217 12.7071L10.9394 15.4743L9.34753 17.0662L4.28133 12ZM12.0001 16.535L10.0546 18.4804C9.66412 18.8709 9.03095 18.8709 8.64043 18.4804L2.86711 12.7071C2.47659 12.3166 2.47659 11.6834 2.86711 11.2929L8.64043 5.51958C9.03095 5.12905 9.66412 5.12905 10.0546 5.51958L12.0001 7.465L13.9455 5.51958C14.336 5.12905 14.9692 5.12905 15.3597 5.51958L21.133 11.2929C21.5235 11.6834 21.5235 12.3166 21.133 12.7071L15.3597 18.4804C14.9692 18.8709 14.336 18.8709 13.9455 18.4804L12.0001 16.535ZM13.0607 8.52566L14.6526 6.93379L19.7188 12L14.6526 17.0662L13.0607 15.4743L15.828 12.7071C16.2185 12.3166 16.2185 11.6834 15.828 11.2929L13.0607 8.52566ZM12.0001 9.58632L14.4137 12L12.0001 14.4137L9.58638 12L12.0001 9.58632Z"
    />
  </FxSvg>
);
export const FxDetailIcon = (props: FxSvgProps) => (
  <FxSvg width={24} height={24} viewBox="0 0 24 24" fill="none" {...props}>
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M3.25 7C3.25 6.58579 3.58579 6.25 4 6.25H20C20.4142 6.25 20.75 6.58579 20.75 7C20.75 7.41421 20.4142 7.75 20 7.75H4C3.58579 7.75 3.25 7.41421 3.25 7ZM3.25 12C3.25 11.5858 3.58579 11.25 4 11.25H20C20.4142 11.25 20.75 11.5858 20.75 12C20.75 12.4142 20.4142 12.75 20 12.75H4C3.58579 12.75 3.25 12.4142 3.25 12ZM4 16.25C3.58579 16.25 3.25 16.5858 3.25 17C3.25 17.4142 3.58579 17.75 4 17.75H14C14.4142 17.75 14.75 17.4142 14.75 17C14.75 16.5858 14.4142 16.25 14 16.25H4Z"
    />
  </FxSvg>
);
export const FxExternalLinkIcon = (props: FxSvgProps) => (
  <FxSvg width={24} height={24} viewBox="0 0 24 24" fill="none" {...props}>
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M14.625 3.375C14.625 2.96079 14.9608 2.625 15.375 2.625H20.625C21.0392 2.625 21.375 2.96079 21.375 3.375V8.625C21.375 9.03921 21.0392 9.375 20.625 9.375C20.2108 9.375 19.875 9.03921 19.875 8.625V5.18566L11.4053 13.6553C11.1124 13.9482 10.6376 13.9482 10.3447 13.6553C10.0518 13.3624 10.0518 12.8876 10.3447 12.5947L18.8143 4.125H15.375C14.9608 4.125 14.625 3.78921 14.625 3.375ZM2.625 5.875C2.625 4.9085 3.4085 4.125 4.375 4.125H11.625C12.0392 4.125 12.375 4.46079 12.375 4.875C12.375 5.28921 12.0392 5.625 11.625 5.625H4.375C4.23693 5.625 4.125 5.73693 4.125 5.875V19.625C4.125 19.7631 4.23693 19.875 4.375 19.875H18.125C18.2631 19.875 18.375 19.7631 18.375 19.625V12.375C18.375 11.9608 18.7108 11.625 19.125 11.625C19.5392 11.625 19.875 11.9608 19.875 12.375V19.625C19.875 20.5915 19.0915 21.375 18.125 21.375H4.375C3.4085 21.375 2.625 20.5915 2.625 19.625V5.875Z"
    />
  </FxSvg>
);
export const FxPoolIcon = (props: FxSvgProps) => (
  <FxSvg width={24} height={24} viewBox="0 0 24 24" fill="none" {...props}>
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M4.675 3.59375C4.38823 3.59375 4.11321 3.70767 3.91044 3.91044C3.70767 4.11321 3.59375 4.38823 3.59375 4.675C3.59375 4.96177 3.70767 5.23679 3.91044 5.43956C4.11321 5.64233 4.38823 5.75625 4.675 5.75625C4.96177 5.75625 5.23679 5.64233 5.43956 5.43956C5.64233 5.23679 5.75625 4.96177 5.75625 4.675C5.75625 4.38823 5.64233 4.11321 5.43956 3.91044C5.23679 3.70767 4.96177 3.59375 4.675 3.59375ZM2.84978 2.84978C3.33386 2.3657 3.99041 2.09375 4.675 2.09375C5.35959 2.09375 6.01614 2.3657 6.50022 2.84978C6.9843 3.33386 7.25625 3.99041 7.25625 4.675C7.25625 5.35959 6.9843 6.01614 6.50022 6.50022C6.01614 6.9843 5.35959 7.25625 4.675 7.25625C3.99041 7.25625 3.33386 6.9843 2.84978 6.50022C2.3657 6.01614 2.09375 5.35959 2.09375 4.675C2.09375 3.99041 2.3657 3.33386 2.84978 2.84978ZM12 3.59375C11.7132 3.59375 11.4382 3.70767 11.2354 3.91044C11.0327 4.11321 10.9187 4.38824 10.9187 4.675C10.9187 4.96177 11.0327 5.23679 11.2354 5.43956C11.4382 5.64233 11.7132 5.75625 12 5.75625C12.2868 5.75625 12.5618 5.64233 12.7646 5.43956C12.9673 5.23679 13.0812 4.96177 13.0812 4.675C13.0813 4.38824 12.9673 4.11321 12.7646 3.91044C12.5618 3.70767 12.2868 3.59375 12 3.59375ZM10.1748 2.84978C10.6589 2.3657 11.3154 2.09375 12 2.09375C12.6846 2.09375 13.3411 2.3657 13.8252 2.84978C14.3093 3.33386 14.5813 3.99041 14.5812 4.675C14.5812 5.35959 14.3093 6.01614 13.8252 6.50022C13.3411 6.9843 12.6846 7.25625 12 7.25625C11.572 7.25625 11.1549 7.14995 10.7842 6.95197L6.95197 10.7842C7.14995 11.1549 7.25625 11.572 7.25625 12C7.25625 12.6846 6.9843 13.3411 6.50022 13.8252C6.01614 14.3093 5.35959 14.5812 4.675 14.5812C3.99041 14.5812 3.33386 14.3093 2.84978 13.8252C2.3657 13.3411 2.09375 12.6846 2.09375 12C2.09375 11.3154 2.3657 10.6589 2.84978 10.1748C3.33386 9.6907 3.99041 9.41875 4.675 9.41875C5.10324 9.41875 5.52051 9.52517 5.89145 9.72335L9.72335 5.89145C9.52517 5.52051 9.41875 5.10324 9.41875 4.675C9.41875 3.99041 9.6907 3.33386 10.1748 2.84978ZM19.325 3.59375C19.0382 3.59375 18.7632 3.70767 18.5604 3.91044C18.3577 4.11321 18.2437 4.38824 18.2437 4.675C18.2437 4.96177 18.3577 5.23679 18.5604 5.43956C18.7632 5.64233 19.0382 5.75625 19.325 5.75625C19.6118 5.75625 19.8868 5.64233 20.0896 5.43956C20.2923 5.23679 20.4062 4.96177 20.4062 4.675C20.4062 4.38824 20.2923 4.11321 20.0896 3.91044C19.8868 3.70767 19.6118 3.59375 19.325 3.59375ZM17.4998 2.84978C17.9839 2.3657 18.6404 2.09375 19.325 2.09375C20.0096 2.09375 20.6661 2.3657 21.1502 2.84978C21.6343 3.33386 21.9062 3.99041 21.9062 4.675C21.9062 5.35959 21.6343 6.01614 21.1502 6.50022C20.6661 6.9843 20.0096 7.25625 19.325 7.25625C18.897 7.25625 18.4799 7.14995 18.1091 6.95197L14.277 10.7842C14.4749 11.1549 14.5812 11.572 14.5812 12C14.5812 12.6846 14.3093 13.3411 13.8252 13.8252C13.3411 14.3093 12.6846 14.5812 12 14.5812C11.572 14.5812 11.1549 14.4749 10.7842 14.277L6.95197 18.1091C7.14995 18.4799 7.25625 18.897 7.25625 19.325C7.25625 20.0096 6.9843 20.6661 6.50022 21.1502C6.01614 21.6343 5.35959 21.9062 4.675 21.9062C3.99041 21.9062 3.33386 21.6343 2.84978 21.1502C2.3657 20.6661 2.09375 20.0096 2.09375 19.325C2.09375 18.6404 2.3657 17.9839 2.84978 17.4998C3.33386 17.0157 3.99041 16.7437 4.675 16.7437C5.10324 16.7437 5.52051 16.8502 5.89145 17.0484L9.72335 13.2164C9.52517 12.8455 9.41875 12.4282 9.41875 12C9.41875 11.3154 9.6907 10.6589 10.1748 10.1748C10.6589 9.6907 11.3154 9.41875 12 9.41875C12.4282 9.41875 12.8455 9.52517 13.2164 9.72335L17.0484 5.89145C16.8502 5.52051 16.7437 5.10324 16.7437 4.675C16.7437 3.99041 17.0157 3.33386 17.4998 2.84978ZM4.675 10.9187C4.38824 10.9187 4.11321 11.0327 3.91044 11.2354C3.70767 11.4382 3.59375 11.7132 3.59375 12C3.59375 12.2868 3.70767 12.5618 3.91044 12.7646C4.11321 12.9673 4.38824 13.0812 4.675 13.0812C4.96177 13.0812 5.23679 12.9673 5.43956 12.7646C5.64233 12.5618 5.75625 12.2868 5.75625 12C5.75625 11.7132 5.64233 11.4382 5.43956 11.2354C5.23679 11.0327 4.96177 10.9187 4.675 10.9187ZM12 10.9187C11.7132 10.9187 11.4382 11.0327 11.2354 11.2354C11.0327 11.4382 10.9187 11.7132 10.9187 12C10.9187 12.2868 11.0327 12.5618 11.2354 12.7646C11.4382 12.9673 11.7132 13.0812 12 13.0812C12.2868 13.0812 12.5618 12.9673 12.7646 12.7646C12.9673 12.5618 13.0812 12.2868 13.0812 12C13.0813 11.7132 12.9673 11.4382 12.7646 11.2354C12.5618 11.0327 12.2868 10.9187 12 10.9187ZM19.325 10.9187C19.0382 10.9187 18.7632 11.0327 18.5604 11.2354C18.3577 11.4382 18.2437 11.7132 18.2437 12C18.2437 12.2868 18.3577 12.5618 18.5604 12.7646C18.7632 12.9673 19.0382 13.0812 19.325 13.0812C19.6118 13.0812 19.8868 12.9673 20.0896 12.7646C20.2923 12.5618 20.4062 12.2868 20.4062 12C20.4062 11.7132 20.2923 11.4382 20.0896 11.2354C19.8868 11.0327 19.6118 10.9187 19.325 10.9187ZM17.4998 10.1748C17.9839 9.6907 18.6404 9.41875 19.325 9.41875C20.0096 9.41875 20.6661 9.6907 21.1502 10.1748C21.6343 10.6589 21.9062 11.3154 21.9062 12C21.9062 12.6846 21.6343 13.3411 21.1502 13.8252C20.6661 14.3093 20.0096 14.5812 19.325 14.5812C18.897 14.5812 18.4799 14.4749 18.1091 14.277L14.277 18.1091C14.4749 18.4799 14.5812 18.897 14.5812 19.325C14.5812 20.0096 14.3093 20.6661 13.8252 21.1502C13.3411 21.6343 12.6846 21.9062 12 21.9062C11.3154 21.9062 10.6589 21.6343 10.1748 21.1502C9.6907 20.6661 9.41875 20.0096 9.41875 19.325C9.41875 18.6404 9.6907 17.9839 10.1748 17.4998C10.6589 17.0157 11.3154 16.7437 12 16.7437C12.4282 16.7437 12.8455 16.8502 13.2164 17.0484L17.0484 13.2164C16.8502 12.8455 16.7437 12.4282 16.7437 12C16.7437 11.3154 17.0157 10.6589 17.4998 10.1748ZM4.675 18.2437C4.38824 18.2437 4.11321 18.3577 3.91044 18.5604C3.70767 18.7632 3.59375 19.0382 3.59375 19.325C3.59375 19.6118 3.70767 19.8868 3.91044 20.0896C4.11321 20.2923 4.38824 20.4062 4.675 20.4062C4.96177 20.4062 5.23679 20.2923 5.43956 20.0896C5.64233 19.8868 5.75625 19.6118 5.75625 19.325C5.75625 19.0382 5.64233 18.7632 5.43956 18.5604C5.23679 18.3577 4.96177 18.2437 4.675 18.2437ZM12 18.2437C11.7132 18.2437 11.4382 18.3577 11.2354 18.5604C11.0327 18.7632 10.9187 19.0382 10.9187 19.325C10.9187 19.6118 11.0327 19.8868 11.2354 20.0896C11.4382 20.2923 11.7132 20.4062 12 20.4062C12.2868 20.4062 12.5618 20.2923 12.7646 20.0896C12.9673 19.8868 13.0812 19.6118 13.0812 19.325C13.0813 19.0382 12.9673 18.7632 12.7646 18.5604C12.5618 18.3577 12.2868 18.2437 12 18.2437ZM19.325 18.2437C19.0382 18.2437 18.7632 18.3577 18.5604 18.5604C18.3577 18.7632 18.2437 19.0382 18.2437 19.325C18.2437 19.6118 18.3577 19.8868 18.5604 20.0896C18.7632 20.2923 19.0382 20.4062 19.325 20.4062C19.6118 20.4062 19.8868 20.2923 20.0896 20.0896C20.2923 19.8868 20.4062 19.6118 20.4062 19.325C20.4062 19.0382 20.2923 18.7632 20.0896 18.5604C19.8868 18.3577 19.6118 18.2437 19.325 18.2437ZM17.4998 17.4998C17.9839 17.0157 18.6404 16.7437 19.325 16.7437C20.0096 16.7437 20.6661 17.0157 21.1502 17.4998C21.6343 17.9839 21.9062 18.6404 21.9062 19.325C21.9062 20.0096 21.6343 20.6661 21.1502 21.1502C20.6661 21.6343 20.0096 21.9062 19.325 21.9062C18.6404 21.9062 17.9839 21.6343 17.4998 21.1502C17.0157 20.6661 16.7437 20.0096 16.7437 19.325C16.7437 18.6404 17.0157 17.9839 17.4998 17.4998Z"
    />
  </FxSvg>
);
export const FxGridIcon = (props: FxSvgProps) => (
  <FxSvg width={24} height={24} viewBox="0 0 24 24" {...props}>
    <Path d="M1.5 3.75C1.5 3.15326 1.73705 2.58097 2.15901 2.15901C2.58097 1.73705 3.15326 1.5 3.75 1.5H8.25C8.84674 1.5 9.41903 1.73705 9.84099 2.15901C10.2629 2.58097 10.5 3.15326 10.5 3.75V8.25C10.5 8.84674 10.2629 9.41903 9.84099 9.84099C9.41903 10.2629 8.84674 10.5 8.25 10.5H3.75C3.15326 10.5 2.58097 10.2629 2.15901 9.84099C1.73705 9.41903 1.5 8.84674 1.5 8.25V3.75ZM3.75 3C3.55109 3 3.36032 3.07902 3.21967 3.21967C3.07902 3.36032 3 3.55109 3 3.75V8.25C3 8.44891 3.07902 8.63968 3.21967 8.78033C3.36032 8.92098 3.55109 9 3.75 9H8.25C8.44891 9 8.63968 8.92098 8.78033 8.78033C8.92098 8.63968 9 8.44891 9 8.25V3.75C9 3.55109 8.92098 3.36032 8.78033 3.21967C8.63968 3.07902 8.44891 3 8.25 3H3.75ZM13.5 3.75C13.5 3.15326 13.7371 2.58097 14.159 2.15901C14.581 1.73705 15.1533 1.5 15.75 1.5H20.25C20.8467 1.5 21.419 1.73705 21.841 2.15901C22.2629 2.58097 22.5 3.15326 22.5 3.75V8.25C22.5 8.84674 22.2629 9.41903 21.841 9.84099C21.419 10.2629 20.8467 10.5 20.25 10.5H15.75C15.1533 10.5 14.581 10.2629 14.159 9.84099C13.7371 9.41903 13.5 8.84674 13.5 8.25V3.75ZM15.75 3C15.5511 3 15.3603 3.07902 15.2197 3.21967C15.079 3.36032 15 3.55109 15 3.75V8.25C15 8.44891 15.079 8.63968 15.2197 8.78033C15.3603 8.92098 15.5511 9 15.75 9H20.25C20.4489 9 20.6397 8.92098 20.7803 8.78033C20.921 8.63968 21 8.44891 21 8.25V3.75C21 3.55109 20.921 3.36032 20.7803 3.21967C20.6397 3.07902 20.4489 3 20.25 3H15.75ZM1.5 15.75C1.5 15.1533 1.73705 14.581 2.15901 14.159C2.58097 13.7371 3.15326 13.5 3.75 13.5H8.25C8.84674 13.5 9.41903 13.7371 9.84099 14.159C10.2629 14.581 10.5 15.1533 10.5 15.75V20.25C10.5 20.8467 10.2629 21.419 9.84099 21.841C9.41903 22.2629 8.84674 22.5 8.25 22.5H3.75C3.15326 22.5 2.58097 22.2629 2.15901 21.841C1.73705 21.419 1.5 20.8467 1.5 20.25V15.75ZM3.75 15C3.55109 15 3.36032 15.079 3.21967 15.2197C3.07902 15.3603 3 15.5511 3 15.75V20.25C3 20.4489 3.07902 20.6397 3.21967 20.7803C3.36032 20.921 3.55109 21 3.75 21H8.25C8.44891 21 8.63968 20.921 8.78033 20.7803C8.92098 20.6397 9 20.4489 9 20.25V15.75C9 15.5511 8.92098 15.3603 8.78033 15.2197C8.63968 15.079 8.44891 15 8.25 15H3.75ZM13.5 15.75C13.5 15.1533 13.7371 14.581 14.159 14.159C14.581 13.7371 15.1533 13.5 15.75 13.5H20.25C20.8467 13.5 21.419 13.7371 21.841 14.159C22.2629 14.581 22.5 15.1533 22.5 15.75V20.25C22.5 20.8467 22.2629 21.419 21.841 21.841C21.419 22.2629 20.8467 22.5 20.25 22.5H15.75C15.1533 22.5 14.581 22.2629 14.159 21.841C13.7371 21.419 13.5 20.8467 13.5 20.25V15.75ZM15.75 15C15.5511 15 15.3603 15.079 15.2197 15.2197C15.079 15.3603 15 15.5511 15 15.75V20.25C15 20.4489 15.079 20.6397 15.2197 20.7803C15.3603 20.921 15.5511 21 15.75 21H20.25C20.4489 21 20.6397 20.921 20.7803 20.7803C20.921 20.6397 21 20.4489 21 20.25V15.75C21 15.5511 20.921 15.3603 20.7803 15.2197C20.6397 15.079 20.4489 15 20.25 15H15.75Z" />
  </FxSvg>
);
export const FxListIcon = (props: FxSvgProps) => (
  <FxSvg width={24} height={24} viewBox="0 0 24 24" {...props}>
    <Path
      d="M3.75 18C3.75 17.8011 3.82902 17.6103 3.96967 17.4697C4.11032 17.329 4.30109 17.25 4.5 17.25H19.5C19.6989 17.25 19.8897 17.329 20.0303 17.4697C20.171 17.6103 20.25 17.8011 20.25 18C20.25 18.1989 20.171 18.3897 20.0303 18.5303C19.8897 18.671 19.6989 18.75 19.5 18.75H4.5C4.30109 18.75 4.11032 18.671 3.96967 18.5303C3.82902 18.3897 3.75 18.1989 3.75 18ZM3.75 12C3.75 11.8011 3.82902 11.6103 3.96967 11.4697C4.11032 11.329 4.30109 11.25 4.5 11.25H19.5C19.6989 11.25 19.8897 11.329 20.0303 11.4697C20.171 11.6103 20.25 11.8011 20.25 12C20.25 12.1989 20.171 12.3897 20.0303 12.5303C19.8897 12.671 19.6989 12.75 19.5 12.75H4.5C4.30109 12.75 4.11032 12.671 3.96967 12.5303C3.82902 12.3897 3.75 12.1989 3.75 12ZM3.75 6C3.75 5.80109 3.82902 5.61032 3.96967 5.46967C4.11032 5.32902 4.30109 5.25 4.5 5.25H19.5C19.6989 5.25 19.8897 5.32902 20.0303 5.46967C20.171 5.61032 20.25 5.80109 20.25 6C20.25 6.19891 20.171 6.38968 20.0303 6.53033C19.8897 6.67098 19.6989 6.75 19.5 6.75H4.5C4.30109 6.75 4.11032 6.67098 3.96967 6.53033C3.82902 6.38968 3.75 6.19891 3.75 6Z"
      fillRule="evenodd"
      clipRule="evenodd"
    />
  </FxSvg>
);
export const FxPlusIcon = (props: FxSvgProps) => (
  <FxSvg width={24} height={24} viewBox="0 0 24 24" {...props}>
    <Path
      d="M12.5 2.75C12.5 2.33579 12.1642 2 11.75 2C11.3358 2 11 2.33579 11 2.75V11H2.75C2.33579 11 2 11.3358 2 11.75C2 12.1642 2.33579 12.5 2.75 12.5H11V20.75C11 21.1642 11.3358 21.5 11.75 21.5C12.1642 21.5 12.5 21.1642 12.5 20.75V12.5H20.75C21.1642 12.5 21.5 12.1642 21.5 11.75C21.5 11.3358 21.1642 11 20.75 11H12.5V2.75Z"
      fillRule="evenodd"
      clipRule="evenodd"
    />
  </FxSvg>
);
