export const POOL_STORAGE_ABI = [
  {
    "constant": true,
    "inputs": [{ "name": "", "type": "uint256" }],
    "name": "poolIds",
    "outputs": [{ "name": "", "type": "uint32" }],
    "payable": false,
    "stateMutability": "view",
    "type": "function"
  },
  {
    "constant": true,
    "inputs": [{ "name": "poolId", "type": "uint32" }],
    "name": "pools",
    "outputs": [
      { "name": "creator", "type": "address" },
      { "name": "id", "type": "uint32" },
      { "name": "maxChallengeResponsePeriod", "type": "uint32" },
      { "name": "memberCount", "type": "uint32" },
      { "name": "maxMembers", "type": "uint32" },
      { "name": "requiredTokens", "type": "uint256" },
      { "name": "minPingTime", "type": "uint256" },
      { "name": "name", "type": "string" },
      { "name": "region", "type": "string" }
    ],
    "payable": false,
    "stateMutability": "view",
    "type": "function"
  }
];

export const REWARD_ENGINE_ABI = [
  {
    "inputs": [{"internalType": "uint256", "name": "poolId", "type": "uint256"}],
    "name": "claimRewards",
    "outputs": [],
    "stateMutability": "nonpayable",
    "type": "function"
  }
];

export const FULA_TOKEN_ABI = [
  {
    "inputs": [{"internalType": "address", "name": "account", "type": "address"}],
    "name": "balanceOf",
    "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}],
    "stateMutability": "view",
    "type": "function"
  },
  {
    "inputs": [],
    "name": "decimals",
    "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}],
    "stateMutability": "view",
    "type": "function"
  },
  {
    "inputs": [
      {"internalType": "address", "name": "to", "type": "address"},
      {"internalType": "uint256", "name": "amount", "type": "uint256"}
    ],
    "name": "transfer",
    "outputs": [{"internalType": "bool", "name": "", "type": "bool"}],
    "stateMutability": "nonpayable",
    "type": "function"
  }
];