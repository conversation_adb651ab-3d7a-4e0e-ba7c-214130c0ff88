PODS:
  - boost (1.76.0)
  - CryptoSwift (1.7.1)
  - DoubleConversion (1.1.6)
  - FBLazyVector (0.72.10)
  - FBReactNativeSpec (0.72.10):
    - RCT-<PERSON>olly (= 2021.07.22.00)
    - RCTRequired (= 0.72.10)
    - RCTTypeSafety (= 0.72.10)
    - React-Core (= 0.72.10)
    - React-jsi (= 0.72.10)
    - ReactCommon/turbomodule/core (= 0.72.10)
  - Firebase/CoreOnly (10.20.0):
    - FirebaseCore (= 10.20.0)
  - Firebase/Crashlytics (10.20.0):
    - Firebase/CoreOnly
    - FirebaseCrashlytics (~> 10.20.0)
  - FirebaseCore (10.20.0):
    - FirebaseCoreInternal (~> 10.0)
    - GoogleUtilities/Environment (~> 7.12)
    - GoogleUtilities/Logger (~> 7.12)
  - FirebaseCoreExtension (10.20.0):
    - FirebaseCore (~> 10.0)
  - FirebaseCoreInternal (10.29.0):
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
  - FirebaseCrashlytics (10.20.0):
    - FirebaseCore (~> 10.5)
    - FirebaseInstallations (~> 10.0)
    - FirebaseSessions (~> 10.5)
    - GoogleDataTransport (~> 9.2)
    - GoogleUtilities/Environment (~> 7.8)
    - nanopb (< 2.30910.0, >= 2.30908.0)
    - PromisesObjC (~> 2.1)
  - FirebaseInstallations (10.29.0):
    - FirebaseCore (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - PromisesObjC (~> 2.1)
  - FirebaseSessions (10.29.0):
    - FirebaseCore (~> 10.5)
    - FirebaseCoreExtension (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleDataTransport (~> 9.2)
    - GoogleUtilities/Environment (~> 7.13)
    - GoogleUtilities/UserDefaults (~> 7.13)
    - nanopb (< 2.30911.0, >= 2.30908.0)
    - PromisesSwift (~> 2.1)
  - fmt (6.2.1)
  - Fula (1.54.16)
  - glog (0.3.5)
  - GoogleDataTransport (9.4.1):
    - GoogleUtilities/Environment (~> 7.7)
    - nanopb (< 2.30911.0, >= 2.30908.0)
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/Environment (7.13.3):
    - GoogleUtilities/Privacy
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/Logger (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - "GoogleUtilities/NSData+zlib (7.13.3)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (7.13.3)
  - GoogleUtilities/UserDefaults (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - hermes-engine (0.72.10):
    - hermes-engine/Pre-built (= 0.72.10)
  - hermes-engine/Pre-built (0.72.10)
  - libevent (2.1.12)
  - nanopb (2.30909.1):
    - nanopb/decode (= 2.30909.1)
    - nanopb/encode (= 2.30909.1)
  - nanopb/decode (2.30909.1)
  - nanopb/encode (2.30909.1)
  - PromisesObjC (2.4.0)
  - PromisesSwift (2.4.0):
    - PromisesObjC (= 2.4.0)
  - RCT-Folly (2021.07.22.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Default (= 2021.07.22.00)
  - RCT-Folly/Default (2021.07.22.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
  - RCT-Folly/Futures (2021.07.22.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - libevent
  - RCTRequired (0.72.10)
  - RCTTypeSafety (0.72.10):
    - FBLazyVector (= 0.72.10)
    - RCTRequired (= 0.72.10)
    - React-Core (= 0.72.10)
  - React (0.72.10):
    - React-Core (= 0.72.10)
    - React-Core/DevSupport (= 0.72.10)
    - React-Core/RCTWebSocket (= 0.72.10)
    - React-RCTActionSheet (= 0.72.10)
    - React-RCTAnimation (= 0.72.10)
    - React-RCTBlob (= 0.72.10)
    - React-RCTImage (= 0.72.10)
    - React-RCTLinking (= 0.72.10)
    - React-RCTNetwork (= 0.72.10)
    - React-RCTSettings (= 0.72.10)
    - React-RCTText (= 0.72.10)
    - React-RCTVibration (= 0.72.10)
  - React-callinvoker (0.72.10)
  - React-Codegen (0.72.10):
    - DoubleConversion
    - FBReactNativeSpec
    - glog
    - hermes-engine
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-jsi
    - React-jsiexecutor
    - React-NativeModulesApple
    - React-rncore
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - React-Core (0.72.10):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default (= 0.72.10)
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/CoreModulesHeaders (0.72.10):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/Default (0.72.10):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/DevSupport (0.72.10):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default (= 0.72.10)
    - React-Core/RCTWebSocket (= 0.72.10)
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector (= 0.72.10)
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTActionSheetHeaders (0.72.10):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTAnimationHeaders (0.72.10):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTBlobHeaders (0.72.10):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTImageHeaders (0.72.10):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTLinkingHeaders (0.72.10):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTNetworkHeaders (0.72.10):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTSettingsHeaders (0.72.10):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTTextHeaders (0.72.10):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTVibrationHeaders (0.72.10):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTWebSocket (0.72.10):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default (= 0.72.10)
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-CoreModules (0.72.10):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.72.10)
    - React-Codegen (= 0.72.10)
    - React-Core/CoreModulesHeaders (= 0.72.10)
    - React-jsi (= 0.72.10)
    - React-RCTBlob
    - React-RCTImage (= 0.72.10)
    - ReactCommon/turbomodule/core (= 0.72.10)
    - SocketRocket (= 0.6.1)
  - React-cxxreact (0.72.10):
    - boost (= 1.76.0)
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker (= 0.72.10)
    - React-debug (= 0.72.10)
    - React-jsi (= 0.72.10)
    - React-jsinspector (= 0.72.10)
    - React-logger (= 0.72.10)
    - React-perflogger (= 0.72.10)
    - React-runtimeexecutor (= 0.72.10)
  - React-debug (0.72.10)
  - React-hermes (0.72.10):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - RCT-Folly/Futures (= 2021.07.22.00)
    - React-cxxreact (= 0.72.10)
    - React-jsi
    - React-jsiexecutor (= 0.72.10)
    - React-jsinspector (= 0.72.10)
    - React-perflogger (= 0.72.10)
  - React-jsi (0.72.10):
    - boost (= 1.76.0)
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
  - React-jsiexecutor (0.72.10):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-cxxreact (= 0.72.10)
    - React-jsi (= 0.72.10)
    - React-perflogger (= 0.72.10)
  - React-jsinspector (0.72.10)
  - React-logger (0.72.10):
    - glog
  - react-native-background-timer (2.4.1):
    - React-Core
  - react-native-ble-manager (11.5.7):
    - React-Core
  - react-native-compat (2.17.0):
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
  - react-native-config (1.5.1):
    - react-native-config/App (= 1.5.1)
  - react-native-config/App (1.5.1):
    - React-Core
  - react-native-fula (1.55.11):
    - CryptoSwift (~> 1.7.1)
    - Fula (~> 1.54.16)
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
    - Wnfs (= 1.1.1)
  - react-native-get-random-values (1.9.0):
    - React-Core
  - react-native-netinfo (9.3.10):
    - React-Core
  - react-native-pager-view (6.2.0):
    - React-Core
  - react-native-randombytes (3.6.1):
    - React-Core
  - react-native-safe-area-context (4.6.3):
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - ReactCommon/turbomodule/core
  - react-native-skia (0.1.196):
    - React
    - React-callinvoker
    - React-Core
  - react-native-slider (4.4.2):
    - React-Core
  - react-native-webview (13.2.2):
    - React-Core
  - react-native-zeroconf (0.13.8):
    - React-Core
  - React-NativeModulesApple (0.72.10):
    - hermes-engine
    - React-callinvoker
    - React-Core
    - React-cxxreact
    - React-jsi
    - React-runtimeexecutor
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - React-perflogger (0.72.10)
  - React-RCTActionSheet (0.72.10):
    - React-Core/RCTActionSheetHeaders (= 0.72.10)
  - React-RCTAnimation (0.72.10):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.72.10)
    - React-Codegen (= 0.72.10)
    - React-Core/RCTAnimationHeaders (= 0.72.10)
    - React-jsi (= 0.72.10)
    - ReactCommon/turbomodule/core (= 0.72.10)
  - React-RCTAppDelegate (0.72.10):
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-CoreModules
    - React-hermes
    - React-NativeModulesApple
    - React-RCTImage
    - React-RCTNetwork
    - React-runtimescheduler
    - ReactCommon/turbomodule/core
  - React-RCTBlob (0.72.10):
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Codegen (= 0.72.10)
    - React-Core/RCTBlobHeaders (= 0.72.10)
    - React-Core/RCTWebSocket (= 0.72.10)
    - React-jsi (= 0.72.10)
    - React-RCTNetwork (= 0.72.10)
    - ReactCommon/turbomodule/core (= 0.72.10)
  - React-RCTImage (0.72.10):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.72.10)
    - React-Codegen (= 0.72.10)
    - React-Core/RCTImageHeaders (= 0.72.10)
    - React-jsi (= 0.72.10)
    - React-RCTNetwork (= 0.72.10)
    - ReactCommon/turbomodule/core (= 0.72.10)
  - React-RCTLinking (0.72.10):
    - React-Codegen (= 0.72.10)
    - React-Core/RCTLinkingHeaders (= 0.72.10)
    - React-jsi (= 0.72.10)
    - ReactCommon/turbomodule/core (= 0.72.10)
  - React-RCTNetwork (0.72.10):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.72.10)
    - React-Codegen (= 0.72.10)
    - React-Core/RCTNetworkHeaders (= 0.72.10)
    - React-jsi (= 0.72.10)
    - ReactCommon/turbomodule/core (= 0.72.10)
  - React-RCTSettings (0.72.10):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.72.10)
    - React-Codegen (= 0.72.10)
    - React-Core/RCTSettingsHeaders (= 0.72.10)
    - React-jsi (= 0.72.10)
    - ReactCommon/turbomodule/core (= 0.72.10)
  - React-RCTText (0.72.10):
    - React-Core/RCTTextHeaders (= 0.72.10)
  - React-RCTVibration (0.72.10):
    - RCT-Folly (= 2021.07.22.00)
    - React-Codegen (= 0.72.10)
    - React-Core/RCTVibrationHeaders (= 0.72.10)
    - React-jsi (= 0.72.10)
    - ReactCommon/turbomodule/core (= 0.72.10)
  - React-rncore (0.72.10)
  - React-runtimeexecutor (0.72.10):
    - React-jsi (= 0.72.10)
  - React-runtimescheduler (0.72.10):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker
    - React-debug
    - React-jsi
    - React-runtimeexecutor
  - React-utils (0.72.10):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-debug
  - ReactCommon/turbomodule/bridging (0.72.10):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker (= 0.72.10)
    - React-cxxreact (= 0.72.10)
    - React-jsi (= 0.72.10)
    - React-logger (= 0.72.10)
    - React-perflogger (= 0.72.10)
  - ReactCommon/turbomodule/core (0.72.10):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker (= 0.72.10)
    - React-cxxreact (= 0.72.10)
    - React-jsi (= 0.72.10)
    - React-logger (= 0.72.10)
    - React-perflogger (= 0.72.10)
  - RNCAsyncStorage (1.18.2):
    - React-Core
  - RNCClipboard (1.14.2):
    - React-Core
  - RNCPicker (2.4.10):
    - React-Core
  - RNDeviceInfo (10.14.0):
    - React-Core
  - RNFBApp (18.9.0):
    - Firebase/CoreOnly (= 10.20.0)
    - React-Core
  - RNFBCrashlytics (18.9.0):
    - Firebase/Crashlytics (= 10.20.0)
    - FirebaseCoreExtension (= 10.20.0)
    - React-Core
    - RNFBApp
  - RNGestureHandler (2.12.1):
    - React-Core
  - RNKeychain (9.0.0):
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
  - RNLocalize (3.2.1):
    - React-Core
  - RNNotifee (7.9.0):
    - React-Core
    - RNNotifee/NotifeeCore (= 7.9.0)
  - RNNotifee/NotifeeCore (7.9.0):
    - React-Core
  - RNPermissions (5.0.0):
    - React-Core
  - RNReanimated (3.3.0):
    - DoubleConversion
    - FBLazyVector
    - glog
    - hermes-engine
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-callinvoker
    - React-Core
    - React-Core/DevSupport
    - React-Core/RCTWebSocket
    - React-CoreModules
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-RCTActionSheet
    - React-RCTAnimation
    - React-RCTAppDelegate
    - React-RCTBlob
    - React-RCTImage
    - React-RCTLinking
    - React-RCTNetwork
    - React-RCTSettings
    - React-RCTText
    - ReactCommon/turbomodule/core
    - Yoga
  - RNScreens (3.22.1):
    - React-Core
    - React-RCTImage
  - RNSVG (12.3.0):
    - React-Core
  - RNWifi (4.13.0):
    - React-Core
  - SocketRocket (0.6.1)
  - Wnfs (1.1.1):
    - WnfsBindings (= 1.1.0)
  - WnfsBindings (1.1.0)
  - Yoga (1.14.0)

DEPENDENCIES:
  - boost (from `../node_modules/react-native/third-party-podspecs/boost.podspec`)
  - DoubleConversion (from `../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec`)
  - FBLazyVector (from `../node_modules/react-native/Libraries/FBLazyVector`)
  - FBReactNativeSpec (from `../node_modules/react-native/React/FBReactNativeSpec`)
  - Fula (from `https://raw.githubusercontent.com/functionland/go-fula/main/Fula.podspec`)
  - glog (from `../node_modules/react-native/third-party-podspecs/glog.podspec`)
  - hermes-engine (from `../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec`)
  - libevent (~> 2.1.12)
  - RCT-Folly (from `../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCTRequired (from `../node_modules/react-native/Libraries/RCTRequired`)
  - RCTTypeSafety (from `../node_modules/react-native/Libraries/TypeSafety`)
  - React (from `../node_modules/react-native/`)
  - React-callinvoker (from `../node_modules/react-native/ReactCommon/callinvoker`)
  - React-Codegen (from `build/generated/ios`)
  - React-Core (from `../node_modules/react-native/`)
  - React-Core/RCTWebSocket (from `../node_modules/react-native/`)
  - React-CoreModules (from `../node_modules/react-native/React/CoreModules`)
  - React-cxxreact (from `../node_modules/react-native/ReactCommon/cxxreact`)
  - React-debug (from `../node_modules/react-native/ReactCommon/react/debug`)
  - React-hermes (from `../node_modules/react-native/ReactCommon/hermes`)
  - React-jsi (from `../node_modules/react-native/ReactCommon/jsi`)
  - React-jsiexecutor (from `../node_modules/react-native/ReactCommon/jsiexecutor`)
  - React-jsinspector (from `../node_modules/react-native/ReactCommon/jsinspector`)
  - React-logger (from `../node_modules/react-native/ReactCommon/logger`)
  - react-native-background-timer (from `../node_modules/react-native-background-timer`)
  - react-native-ble-manager (from `../node_modules/react-native-ble-manager`)
  - "react-native-compat (from `../node_modules/@walletconnect/react-native-compat`)"
  - react-native-config (from `../node_modules/react-native-config`)
  - "react-native-fula (from `../node_modules/@functionland/react-native-fula`)"
  - react-native-get-random-values (from `../node_modules/react-native-get-random-values`)
  - "react-native-netinfo (from `../node_modules/@react-native-community/netinfo`)"
  - react-native-pager-view (from `../node_modules/react-native-pager-view`)
  - react-native-randombytes (from `../node_modules/react-native-randombytes`)
  - react-native-safe-area-context (from `../node_modules/react-native-safe-area-context`)
  - "react-native-skia (from `../node_modules/@shopify/react-native-skia`)"
  - "react-native-slider (from `../node_modules/@react-native-community/slider`)"
  - react-native-webview (from `../node_modules/react-native-webview`)
  - react-native-zeroconf (from `../node_modules/react-native-zeroconf`)
  - React-NativeModulesApple (from `../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios`)
  - React-perflogger (from `../node_modules/react-native/ReactCommon/reactperflogger`)
  - React-RCTActionSheet (from `../node_modules/react-native/Libraries/ActionSheetIOS`)
  - React-RCTAnimation (from `../node_modules/react-native/Libraries/NativeAnimation`)
  - React-RCTAppDelegate (from `../node_modules/react-native/Libraries/AppDelegate`)
  - React-RCTBlob (from `../node_modules/react-native/Libraries/Blob`)
  - React-RCTImage (from `../node_modules/react-native/Libraries/Image`)
  - React-RCTLinking (from `../node_modules/react-native/Libraries/LinkingIOS`)
  - React-RCTNetwork (from `../node_modules/react-native/Libraries/Network`)
  - React-RCTSettings (from `../node_modules/react-native/Libraries/Settings`)
  - React-RCTText (from `../node_modules/react-native/Libraries/Text`)
  - React-RCTVibration (from `../node_modules/react-native/Libraries/Vibration`)
  - React-rncore (from `../node_modules/react-native/ReactCommon`)
  - React-runtimeexecutor (from `../node_modules/react-native/ReactCommon/runtimeexecutor`)
  - React-runtimescheduler (from `../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler`)
  - React-utils (from `../node_modules/react-native/ReactCommon/react/utils`)
  - ReactCommon/turbomodule/core (from `../node_modules/react-native/ReactCommon`)
  - "RNCAsyncStorage (from `../node_modules/@react-native-async-storage/async-storage`)"
  - "RNCClipboard (from `../node_modules/@react-native-clipboard/clipboard`)"
  - "RNCPicker (from `../node_modules/@react-native-picker/picker`)"
  - RNDeviceInfo (from `../node_modules/react-native-device-info`)
  - "RNFBApp (from `../node_modules/@react-native-firebase/app`)"
  - "RNFBCrashlytics (from `../node_modules/@react-native-firebase/crashlytics`)"
  - RNGestureHandler (from `../node_modules/react-native-gesture-handler`)
  - RNKeychain (from `../node_modules/react-native-keychain`)
  - RNLocalize (from `../node_modules/react-native-localize`)
  - "RNNotifee (from `../node_modules/@notifee/react-native`)"
  - RNPermissions (from `../node_modules/react-native-permissions`)
  - RNReanimated (from `../node_modules/react-native-reanimated`)
  - RNScreens (from `../node_modules/react-native-screens`)
  - RNSVG (from `../node_modules/react-native-svg`)
  - RNWifi (from `../node_modules/react-native-wifi-reborn`)
  - Yoga (from `../node_modules/react-native/ReactCommon/yoga`)

SPEC REPOS:
  trunk:
    - CryptoSwift
    - Firebase
    - FirebaseCore
    - FirebaseCoreExtension
    - FirebaseCoreInternal
    - FirebaseCrashlytics
    - FirebaseInstallations
    - FirebaseSessions
    - fmt
    - GoogleDataTransport
    - GoogleUtilities
    - libevent
    - nanopb
    - PromisesObjC
    - PromisesSwift
    - SocketRocket
    - Wnfs
    - WnfsBindings

EXTERNAL SOURCES:
  boost:
    :podspec: "../node_modules/react-native/third-party-podspecs/boost.podspec"
  DoubleConversion:
    :podspec: "../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec"
  FBLazyVector:
    :path: "../node_modules/react-native/Libraries/FBLazyVector"
  FBReactNativeSpec:
    :path: "../node_modules/react-native/React/FBReactNativeSpec"
  Fula:
    :podspec: https://raw.githubusercontent.com/functionland/go-fula/main/Fula.podspec
  glog:
    :podspec: "../node_modules/react-native/third-party-podspecs/glog.podspec"
  hermes-engine:
    :podspec: "../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec"
  RCT-Folly:
    :podspec: "../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec"
  RCTRequired:
    :path: "../node_modules/react-native/Libraries/RCTRequired"
  RCTTypeSafety:
    :path: "../node_modules/react-native/Libraries/TypeSafety"
  React:
    :path: "../node_modules/react-native/"
  React-callinvoker:
    :path: "../node_modules/react-native/ReactCommon/callinvoker"
  React-Codegen:
    :path: build/generated/ios
  React-Core:
    :path: "../node_modules/react-native/"
  React-CoreModules:
    :path: "../node_modules/react-native/React/CoreModules"
  React-cxxreact:
    :path: "../node_modules/react-native/ReactCommon/cxxreact"
  React-debug:
    :path: "../node_modules/react-native/ReactCommon/react/debug"
  React-hermes:
    :path: "../node_modules/react-native/ReactCommon/hermes"
  React-jsi:
    :path: "../node_modules/react-native/ReactCommon/jsi"
  React-jsiexecutor:
    :path: "../node_modules/react-native/ReactCommon/jsiexecutor"
  React-jsinspector:
    :path: "../node_modules/react-native/ReactCommon/jsinspector"
  React-logger:
    :path: "../node_modules/react-native/ReactCommon/logger"
  react-native-background-timer:
    :path: "../node_modules/react-native-background-timer"
  react-native-ble-manager:
    :path: "../node_modules/react-native-ble-manager"
  react-native-compat:
    :path: "../node_modules/@walletconnect/react-native-compat"
  react-native-config:
    :path: "../node_modules/react-native-config"
  react-native-fula:
    :path: "../node_modules/@functionland/react-native-fula"
  react-native-get-random-values:
    :path: "../node_modules/react-native-get-random-values"
  react-native-netinfo:
    :path: "../node_modules/@react-native-community/netinfo"
  react-native-pager-view:
    :path: "../node_modules/react-native-pager-view"
  react-native-randombytes:
    :path: "../node_modules/react-native-randombytes"
  react-native-safe-area-context:
    :path: "../node_modules/react-native-safe-area-context"
  react-native-skia:
    :path: "../node_modules/@shopify/react-native-skia"
  react-native-slider:
    :path: "../node_modules/@react-native-community/slider"
  react-native-webview:
    :path: "../node_modules/react-native-webview"
  react-native-zeroconf:
    :path: "../node_modules/react-native-zeroconf"
  React-NativeModulesApple:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios"
  React-perflogger:
    :path: "../node_modules/react-native/ReactCommon/reactperflogger"
  React-RCTActionSheet:
    :path: "../node_modules/react-native/Libraries/ActionSheetIOS"
  React-RCTAnimation:
    :path: "../node_modules/react-native/Libraries/NativeAnimation"
  React-RCTAppDelegate:
    :path: "../node_modules/react-native/Libraries/AppDelegate"
  React-RCTBlob:
    :path: "../node_modules/react-native/Libraries/Blob"
  React-RCTImage:
    :path: "../node_modules/react-native/Libraries/Image"
  React-RCTLinking:
    :path: "../node_modules/react-native/Libraries/LinkingIOS"
  React-RCTNetwork:
    :path: "../node_modules/react-native/Libraries/Network"
  React-RCTSettings:
    :path: "../node_modules/react-native/Libraries/Settings"
  React-RCTText:
    :path: "../node_modules/react-native/Libraries/Text"
  React-RCTVibration:
    :path: "../node_modules/react-native/Libraries/Vibration"
  React-rncore:
    :path: "../node_modules/react-native/ReactCommon"
  React-runtimeexecutor:
    :path: "../node_modules/react-native/ReactCommon/runtimeexecutor"
  React-runtimescheduler:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler"
  React-utils:
    :path: "../node_modules/react-native/ReactCommon/react/utils"
  ReactCommon:
    :path: "../node_modules/react-native/ReactCommon"
  RNCAsyncStorage:
    :path: "../node_modules/@react-native-async-storage/async-storage"
  RNCClipboard:
    :path: "../node_modules/@react-native-clipboard/clipboard"
  RNCPicker:
    :path: "../node_modules/@react-native-picker/picker"
  RNDeviceInfo:
    :path: "../node_modules/react-native-device-info"
  RNFBApp:
    :path: "../node_modules/@react-native-firebase/app"
  RNFBCrashlytics:
    :path: "../node_modules/@react-native-firebase/crashlytics"
  RNGestureHandler:
    :path: "../node_modules/react-native-gesture-handler"
  RNKeychain:
    :path: "../node_modules/react-native-keychain"
  RNLocalize:
    :path: "../node_modules/react-native-localize"
  RNNotifee:
    :path: "../node_modules/@notifee/react-native"
  RNPermissions:
    :path: "../node_modules/react-native-permissions"
  RNReanimated:
    :path: "../node_modules/react-native-reanimated"
  RNScreens:
    :path: "../node_modules/react-native-screens"
  RNSVG:
    :path: "../node_modules/react-native-svg"
  RNWifi:
    :path: "../node_modules/react-native-wifi-reborn"
  Yoga:
    :path: "../node_modules/react-native/ReactCommon/yoga"

SPEC CHECKSUMS:
  boost: 7dcd2de282d72e344012f7d6564d024930a6a440
  CryptoSwift: d3d18dc357932f7e6d580689e065cf1f176007c1
  DoubleConversion: 5189b271737e1565bdce30deb4a08d647e3f5f54
  FBLazyVector: f91d538f197fa71a7d5b77ec2069d49550c0eb96
  FBReactNativeSpec: 96f9a40956e0a55d333125525af3cdd2d74a7884
  Firebase: 10c8cb12fb7ad2ae0c09ffc86cd9c1ab392a0031
  FirebaseCore: 28045c1560a2600d284b9c45a904fe322dc890b6
  FirebaseCoreExtension: 0659f035b88c5a7a15a9763c48c2e6ca8c0a2977
  FirebaseCoreInternal: df84dd300b561c27d5571684f389bf60b0a5c934
  FirebaseCrashlytics: 81530595edb6d99f1918f723a6c33766a24a4c86
  FirebaseInstallations: 913cf60d0400ebd5d6b63a28b290372ab44590dd
  FirebaseSessions: dbd14adac65ce996228652c1fc3a3f576bdf3ecc
  fmt: ff9d55029c625d3757ed641535fd4a75fedc7ce9
  Fula: 4b50e740f27e79a68ef6e4e5ba450da09a7645b0
  glog: 04b94705f318337d7ead9e6d17c019bd9b1f6b1b
  GoogleDataTransport: 6c09b596d841063d76d4288cc2d2f42cc36e1e2a
  GoogleUtilities: ea963c370a38a8069cc5f7ba4ca849a60b6d7d15
  hermes-engine: 90e4033deb00bee33330a9f15eff0f874bd82f6d
  libevent: 4049cae6c81cdb3654a443be001fb9bdceff7913
  nanopb: d4d75c12cd1316f4a64e3c6963f879ecd4b5e0d5
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  PromisesSwift: 9d77319bbe72ebf6d872900551f7eeba9bce2851
  RCT-Folly: 424b8c9a7a0b9ab2886ffe9c3b041ef628fd4fb1
  RCTRequired: b4d3068afa6f52ec5260a8417053b1f1b421483d
  RCTTypeSafety: a4551b3d338c96435f63bf06d564055c1d3cc0ac
  React: 66caa2a8192a35d7ba466a5fdf5dc06ee4a5f6dd
  React-callinvoker: e5b55e46894c2dd1bcdc19d4f82b0f7f631d1237
  React-Codegen: 0cf41e00026c5eba61f6bdcabd6e4bf659754f33
  React-Core: 2ce84187a00913f287b96753c56c7819ed7d90d5
  React-CoreModules: 893e7c5eed1ef8fe9e1ee1d913581c946e55b305
  React-cxxreact: 075d98dc664c0e9607cc0c45d41dc052bcc7313b
  React-debug: abc6213dcb9eafcf5242cbb194fef4c70c91871f
  React-hermes: 133cfa220ef836406f693ed7db56a509032ce433
  React-jsi: 9b45fd040d575f8ae6771bf1960641a58eb0bdd4
  React-jsiexecutor: 45ef2ec6dcde31b90469175ec76ddac77b91dfc3
  React-jsinspector: de0198127395fec3058140a20c045167f761bb16
  React-logger: dc3a2b174d79c2da635059212747d8d929b54e06
  react-native-background-timer: 17ea5e06803401a379ebf1f20505b793ac44d0fe
  react-native-ble-manager: 59a0c7178d6fa0272a1b86f976469b800ce08eba
  react-native-compat: 2dca63ad5d9c3ca5e001795a5789f688ad9fae6f
  react-native-config: 86038147314e2e6d10ea9972022aa171e6b1d4d8
  react-native-fula: 4079ae5793e30b76f224abda29e1d2790145ebbd
  react-native-get-random-values: dee677497c6a740b71e5612e8dbd83e7539ed5bb
  react-native-netinfo: ccbe1085dffd16592791d550189772e13bf479e2
  react-native-pager-view: 0ccb8bf60e2ebd38b1f3669fa3650ecce81db2df
  react-native-randombytes: 421f1c7d48c0af8dbcd471b0324393ebf8fe7846
  react-native-safe-area-context: 36cc67648134e89465663b8172336a19eeda493d
  react-native-skia: 73b15a92d31e322fd25e5bb7feff226c0a25215e
  react-native-slider: 33b8d190b59d4f67a541061bb91775d53d617d9d
  react-native-webview: b8ec89966713985111a14d6e4bf98d8b54bced0d
  react-native-zeroconf: 5b38b434ccc6ca245c8a5ffd64a4501e67f9edcb
  React-NativeModulesApple: c3e696ff867e4bc212266cbdf7e862e48a0166fd
  React-perflogger: 43287389ea08993c300897a46f95cfac04bb6c1a
  React-RCTActionSheet: 923afe77f9bb89da7c1f98e2730bfc9dde0eed6d
  React-RCTAnimation: afd4d94c5e1f731e32ac99800850be06564ac642
  React-RCTAppDelegate: fb2e1447d014557f29e214fe2eb777442f808a3b
  React-RCTBlob: 167e2c6c3643f093058c51e76ecc653fc8236033
  React-RCTImage: 867de82a17630a08a3fa64b0cd6677dd19bf6eaf
  React-RCTLinking: 885dde8bc5d397c3e72c76315f1f9b5030b3a70e
  React-RCTNetwork: efec71102220b96ac8605d0253debd859ca0c817
  React-RCTSettings: 077065d0a4e925b017fe8538afa574d8fb52391f
  React-RCTText: 7adddb518ac362b2398fedf0c64105e0dab29441
  React-RCTVibration: de6b7218e415d82788e0965f278dddb2ef88b372
  React-rncore: 0cd269d445814dd4aa8643ed794e56fae7797f53
  React-runtimeexecutor: 2b2c09edbca4c9a667428e8c93959f66b3b53140
  React-runtimescheduler: 6ca43e8deadf01ff06b3f01abf8f0e4d508e23c3
  React-utils: 372b83030a74347331636909278bf0a60ec30d59
  ReactCommon: 38824bfffaf4c51fbe03a2730b4fd874ef34d67b
  RNCAsyncStorage: ddc4ee162bfd41b0d2c68bf2d95acd81dd7f1f93
  RNCClipboard: 5e503962f0719ace8f7fdfe9c60282b526305c85
  RNCPicker: 0bc2f0a29abcca7b7ed44a2d036aac9ab6d25700
  RNDeviceInfo: 59344c19152c4b2b32283005f9737c5c64b42fba
  RNFBApp: a3e139715386fe79a09c387f2dbeb6890eb05b39
  RNFBCrashlytics: e6d595ed2619e5e8ee3cdfd12c6a62e470280a03
  RNGestureHandler: c0d04458598fcb26052494ae23dda8f8f5162b13
  RNKeychain: f85d33839fc34b5fe720d07eb788ef1d2112a154
  RNLocalize: 4f22418187ecd5ca693231093ff1d912d1b3c9bc
  RNNotifee: 935f3ea8c134c88cbf8b13ea0c97c72c09ad2116
  RNPermissions: d6b630aeb20d688a5973c66ff64d2a38d800c12a
  RNReanimated: 11ef210bd11e80668ed3708f7a9dd846ae29e99c
  RNScreens: 50ffe2fa2342eabb2d0afbe19f7c1af286bc7fb3
  RNSVG: 302bfc9905bd8122f08966dc2ce2d07b7b52b9f8
  RNWifi: 89e8d602c782b6e0a4e4ecca3e26b62c9c6b5dab
  SocketRocket: f32cd54efbe0f095c4d7594881e52619cfe80b17
  Wnfs: e3020bb0c212642a605625424ed263c8ac280e44
  WnfsBindings: 10742440a02c258dd9c065d8f29f24e7029955c3
  Yoga: d0003f849d2b5224c072cef6568b540d8bb15cd3

PODFILE CHECKSUM: ddbc7f8f7312701e5595b295fb156995ebfb2a9d

COCOAPODS: 1.16.2
