{"extends": "../../tsconfig.base.json", "compilerOptions": {"jsx": "react-jsx", "allowJs": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "strict": true, "noImplicitReturns": true, "declaration": true, "skipLibCheck": true, "skipDefaultLibCheck": true, "noFallthroughCasesInSwitch": true}, "files": [], "include": [], "references": [{"path": "./tsconfig.lib.json"}, {"path": "./tsconfig.spec.json"}]}