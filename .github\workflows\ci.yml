name: CI

on:
  push:
    branches:
      - main
    paths:
      - '**'
  pull_request:
    branches:
      - main
    paths:
      - '**'

jobs:
  build:
    name: Check and build codebase
    runs-on: ubuntu-latest
    strategy:
      matrix:
        node-version: [16.x]
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v2
        with:
          node-version: ${{ matrix.node-version }}

      - name: Get Yarn Cache Directory Path
        id: cache-dir-path
        run: echo "::set-output name=dir::$(yarn cache dir)"

      - name: Use Yarn Cache
        uses: actions/cache@v2
        id: cache
        with:
          path: ${{ steps.cache-dir-path.outputs.dir }}
          key: ${{ runner.os }}-yarn-${{ matrix.node-version }}-${{ hashFiles('./yarn.lock') }}

      - name: Installation ${{ matrix.node-version }}
        run: yarn --prefer-offline --frozen-lockfile --non-interactive

      - name: Check Code ${{ matrix.node-version }}
        run: yarn check-ci
